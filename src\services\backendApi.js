/**
 * Backend API Service
 * Handles communication with the backend API for user management
 */
import { getIdToken } from '../firebase/auth';

// Base API URL - should be set in your .env file
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://api.homara.community';

/**
 * Get authentication headers for API requests
 * @returns {Promise<Object>} Headers object with authentication token
 */
const getAuthHeaders = async () => {
  try {
    // Get Firebase ID token
    const token = await getIdToken();

    if (!token) {
      return {};
    }

    return {
      'Authorization': `Bearer ${token}`
    };
  } catch (error) {
    console.error('Error getting auth headers:', error);
    return {};
  }
};

/**
 * Create a new user in the backend
 * @param {Object} userData - User data
 * @param {string} userData.firebaseUid - Firebase UID
 * @param {string} userData.username - Username
 * @param {string} userData.email - Email
 * @param {string} userData.displayName - Display name
 * @param {string} userData.phoneNumber - Phone number
 * @returns {Promise<Object>} Created user object
 */
export const createUser = async (userData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...await getAuthHeaders()
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create user');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating user in backend:', error);
    throw error;
  }
};

/**
 * Get user profile from the backend
 * @param {string} firebaseUid - Firebase UID
 * @returns {Promise<Object>} User profile
 */
export const getUserProfile = async (firebaseUid) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/user/profile/${firebaseUid}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...await getAuthHeaders()
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get user profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting user profile from backend:', error);
    throw error;
  }
};

/**
 * Update user profile in the backend
 * @param {string} firebaseUid - Firebase UID
 * @param {Object} userData - User data to update
 * @returns {Promise<Object>} Updated user profile
 */
export const updateUserProfile = async (firebaseUid, userData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/user/profile/${firebaseUid}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...await getAuthHeaders()
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update user profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating user profile in backend:', error);
    throw error;
  }
};

export default {
  createUser,
  getUserProfile,
  updateUserProfile
};
