/**
 * Entry Point for Homara Website
 * Initializes and starts the application
 */

import App from './app';

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', async () => {
  try {
    console.log('Homara Website starting...');

    // Create and initialize the app
    const app = new App();
    const initialized = await app.initialize();

    if (initialized) {
      // Start the app
      app.start();
    } else {
      console.error('Failed to initialize the application');
    }
  } catch (error) {
    console.error('Error starting Homara Website:', error);
  }
});

// Export the App class for direct imports
export { default as App } from './app';

// Export services for direct imports
export { default as api } from './services/api';
export { default as communityService } from './services/communityService';
export { default as pointService } from './services/pointService';
export { default as userService } from './services/userService';

// Export utilities for direct imports
export { default as urlParser } from './utils/urlParser';
export { default as constants } from './utils/constants';
export { default as threeUtils } from './utils/threeUtils';

// Export state management for direct imports
export { default as state } from './state';

// Export routing for direct imports
export { default as routing } from './routes';