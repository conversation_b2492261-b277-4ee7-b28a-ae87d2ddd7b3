/**
 * Firebase Authentication Module for Homara
 * This is a simplified version that doesn't use ES6 modules
 */

class FirebaseAuth {
    constructor() {
        this.isInitialized = false;
        this.user = null;
        this.auth = null;
        this.db = null;

        // Get Firebase config from global variable
        this.firebaseConfig = window.FIREBASE_CONFIG;

        // Initialize event listeners
        this.eventListeners = {
            'auth-state-changed': [],
            'registration-success': [],
            'registration-error': [],
            'login-success': [],
            'login-error': []
        };

        // Initialize MongoDB connection for user data
        this.mongoDBConnection = new MongoDBConnection();
    }

    // Initialize Firebase with the provided configuration
    initialize() {
        if (this.isInitialized) return true;

        try {
            console.log('Initializing Firebase with config:', this.firebaseConfig);

            // Check if Firebase is already initialized
            if (firebase.apps.length === 0) {
                // Initialize Firebase
                firebase.initializeApp(this.firebaseConfig);
                console.log('Firebase app initialized');
            } else {
                console.log('Firebase already initialized, using existing app');
            }

            // Get Auth and Firestore instances
            this.auth = firebase.auth();
            this.db = firebase.firestore();

            this.isInitialized = true;
            console.log('Firebase auth and firestore initialized successfully');

            // Set up auth state listener
            this.setupAuthStateListener();

            return true;
        } catch (error) {
            console.error('Error initializing Firebase:', error);
            alert('There was an error initializing Firebase. Please check the console for details.');
            return false;
        }
    }

    // Set up authentication state listener
    setupAuthStateListener() {
        this.auth.onAuthStateChanged((user) => {
            this.user = user;
            this.triggerEvent('auth-state-changed', user);
            console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
        });

        console.log('Auth state listener set up');
    }

    // Register a new user with email, password, username, and phone number
    async registerUser(email, password, username, phoneNumber) {
        if (!this.isInitialized) this.initialize();

        try {
            console.log(`Registering user with email: ${email}, username: ${username}, and phone: ${phoneNumber}`);

            let user;

            try {
                // Try to create a new user with Firebase Authentication
                console.log('Attempting to create new user with Firebase');
                const userCredential = await this.auth.createUserWithEmailAndPassword(email, password);
                user = userCredential.user;
                console.log('User created successfully:', user.uid);
            } catch (authError) {
                // If the email is already in use, try to sign in instead
                if (authError.code === 'auth/email-already-in-use') {
                    console.log('Email already in use, attempting to sign in');
                    try {
                        // Try to sign in with the provided credentials
                        const signInResult = await this.auth.signInWithEmailAndPassword(email, password);
                        user = signInResult.user;
                        console.log('Signed in existing user:', user.uid);
                    } catch (signInError) {
                        // If sign-in fails, it means the password is incorrect
                        console.error('Sign-in failed:', signInError);
                        throw new Error('This email is already registered. If this is your account, please use the correct password.');
                    }
                } else {
                    // For other auth errors, rethrow
                    console.error('Auth error:', authError);
                    throw authError;
                }
            }

            // At this point, we have a valid user (either new or existing)
            console.log('Working with user:', user.uid);

            try {
                // Update profile with display name
                console.log('Updating user profile with display name:', username);
                await user.updateProfile({ displayName: username });
                console.log('Profile updated successfully');
            } catch (profileError) {
                console.error('Error updating profile:', profileError);
                // Continue even if profile update fails
            }

            try {
                // Send email verification if not already verified
                if (!user.emailVerified) {
                    console.log('Sending email verification');
                    try {
                        // Try Firebase's built-in method first
                        await user.sendEmailVerification();
                        console.log('Verification email sent via Firebase');
                    } catch (firebaseVerificationError) {
                        console.error('Firebase verification email failed, trying API:', firebaseVerificationError);

                        // If Firebase method fails, try our API endpoint
                        try {
                            const apiBaseUrl = window.API_BASE_URL || 'http://localhost:3001/api';
                            const response = await fetch(`${apiBaseUrl}/auth/send-verification`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ email: user.email })
                            });

                            if (response.ok) {
                                console.log('Verification email sent via API');
                            } else {
                                throw new Error(`API returned status: ${response.status}`);
                            }
                        } catch (apiVerificationError) {
                            console.error('API verification email failed:', apiVerificationError);
                            // Continue even if both verification methods fail
                        }
                    }
                } else {
                    console.log('Email already verified');
                }
            } catch (verificationError) {
                console.error('Error in verification process:', verificationError);
                // Continue even if email verification fails
            }

            // Store the username and phone number in the user profile or database
            const userData = {
                userId: user.uid,
                username: username,
                email: email,
                // Include the password for the MongoDB API
                password: password, // This is needed for the backend API
                displayName: username,
                phoneNumber: phoneNumber,
                createdAt: new Date(),
                builderID: username, // Use username as builderID
                pointPosition: { x: 0, y: 0, z: 0 }, // Default position
                profile: {
                    bio: '',
                    avatarUrl: '',
                    preferences: {
                        theme: 'system',
                        notifications: true
                    }
                }
            };

            // Save to MongoDB through our API
            console.log('Storing user data in database');
            const dbResult = await this.storeUserData(user.uid, username, phoneNumber, userData);

            if (!dbResult.success) {
                console.error('Failed to store user data:', dbResult.error);

                // Check if this is the "email already in use" error
                if (dbResult.error && dbResult.error.includes("email address is already in use")) {
                    console.log("Email already in use error detected. Firebase account created but MongoDB registration failed.");
                    console.log("User can still use the Firebase account, but MongoDB data will need to be fixed.");

                    // We could add code here to handle this special case, such as:
                    // 1. Deleting the Firebase account and asking the user to try again
                    // 2. Trying again with a different email format
                    // 3. Logging this for admin attention

                    // For now, we'll just log it and continue
                } else {
                    console.log("Other database error occurred, but continuing with Firebase account");
                }

                // Continue even if database storage fails - user can still use Firebase auth
            } else {
                console.log('User data stored successfully');
            }

            this.triggerEvent('registration-success', { user, username, phoneNumber });
            return { success: true, user, username, phoneNumber };
        } catch (error) {
            console.error('Error registering user:', error);

            // Format the error message for better user experience
            let errorMessage = 'An unknown error occurred during registration.';

            if (error.code) {
                switch (error.code) {
                    case 'auth/email-already-in-use':
                        errorMessage = 'This email address is already in use. Please use a different email or try logging in.';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'The email address is not valid. Please check and try again.';
                        break;
                    case 'auth/weak-password':
                        errorMessage = 'The password is too weak. Please use at least 6 characters.';
                        break;
                    case 'auth/operation-not-allowed':
                        errorMessage = 'Email/password accounts are not enabled. Please contact support.';
                        break;
                    default:
                        errorMessage = error.message || 'Registration failed. Please try again.';
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            // Add the error code to the error object for debugging
            const enhancedError = {
                ...error,
                message: errorMessage,
                originalMessage: error.message
            };

            this.triggerEvent('registration-error', enhancedError);
            return { success: false, error: enhancedError };
        }
    }

    // Store additional user data in the database
    async storeUserData(userId, username, phoneNumber, fullUserData = null) {
        try {
            console.log(`Storing user data for ${userId} with username: ${username} and phone: ${phoneNumber}`);

            // Use provided userData or create a basic one
            const userData = fullUserData || {
                userId: userId,
                username: username,
                phoneNumber: phoneNumber,
                // Add a default password for the API if not provided
                password: 'Password123!', // Default strong password
                createdAt: new Date(),
                builderID: username // Use username as builderID
            };

            // Save to MongoDB
            console.log('Saving user data to MongoDB');
            const mongoResult = await this.mongoDBConnection.saveUser(userData);
            console.log('MongoDB result:', mongoResult);

            // Store in Firestore - skip this step for now since we're using MongoDB
            try {
                console.log('Skipping Firestore storage and using MongoDB only');
                /*
                // This code is commented out to avoid Firestore permission errors
                // We'll rely on MongoDB for data storage instead

                console.log('Storing user data in Firestore');

                // Convert any Date objects to timestamps for Firestore
                const firestoreData = { ...userData };
                if (firestoreData.createdAt instanceof Date) {
                    firestoreData.createdAt = firebase.firestore.Timestamp.fromDate(firestoreData.createdAt);
                }

                // Check if document already exists
                const docRef = this.db.collection('users').doc(userId);
                const docSnapshot = await docRef.get();

                if (docSnapshot.exists) {
                    console.log('User document already exists, updating');
                    await docRef.update(firestoreData);
                } else {
                    console.log('Creating new user document');
                    await docRef.set(firestoreData);
                }

                console.log('Firestore data stored successfully');
                */
            } catch (firestoreError) {
                console.error('Error with Firestore operation:', firestoreError);
                // Continue even if Firestore operation fails
            }

            console.log('User data stored successfully');
            return mongoResult;
        } catch (error) {
            console.error('Error storing user data:', error);
            return { success: false, error: error.message };
        }
    }

    // Sign in an existing user
    async signIn(email, password) {
        if (!this.isInitialized) this.initialize();

        try {
            console.log(`Signing in user with email: ${email}`);

            // Sign in with Firebase Authentication
            const userCredential = await this.auth.signInWithEmailAndPassword(email, password);
            const user = userCredential.user;

            this.user = user;
            this.triggerEvent('login-success', user);
            return { success: true, user };
        } catch (error) {
            console.error('Error signing in:', error);
            this.triggerEvent('login-error', error);
            return { success: false, error };
        }
    }

    // Sign out the current user
    async signOut() {
        if (!this.isInitialized) return { success: false, error: 'Firebase not initialized' };

        try {
            // Sign out with Firebase Authentication
            await this.auth.signOut();

            this.user = null;
            console.log('User signed out successfully');
            return { success: true };
        } catch (error) {
            console.error('Error signing out:', error);
            return { success: false, error };
        }
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.user !== null;
    }

    // Get current user
    getCurrentUser() {
        return this.user;
    }

    // Get ID token for the current user
    async getIdToken() {
        if (!this.user) return null;

        try {
            return await this.user.getIdToken();
        } catch (error) {
            console.error('Error getting ID token:', error);
            return null;
        }
    }

    // Event handling methods
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }

    removeEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
    }

    triggerEvent(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => callback(data));
        }
    }
}

// Make the class globally available
window.FirebaseAuth = FirebaseAuth;
