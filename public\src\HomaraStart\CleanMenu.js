/**
 * Clean Menu System
 * Displays the final clean text menu after point animation completes
 */

class CleanMenu {
    constructor() {
        this.container = null;
        this.menuItems = [
            { text: 'JOIN HOMARA', url: 'dev-menu/join-homara/index.html' },
            { text: 'WHY', url: 'dev-menu/why/index.html' },
            { text: 'OUR TIMELINE', url: 'dev-menu/timeline/index.html' }
        ];
        
        this.init();
    }
    
    init() {
        // Create the clean menu container
        this.container = document.createElement('div');
        this.container.id = 'clean-menu-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            width: 500px;
            height: 400px;
            opacity: 0;
            transition: opacity 0.5s ease;
        `;
        document.body.appendChild(this.container);
        
        this.createMenuItems();
    }
    
    createMenuItems() {
        this.menuItems.forEach((item, index) => {
            // Create menu item container
            const menuItem = document.createElement('div');
            menuItem.className = 'clean-menu-item';
            menuItem.style.cssText = `
                position: absolute;
                top: ${index * 100}px;
                right: 0;
                width: 500px;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                cursor: pointer;
                transition: all 0.3s ease;
                padding-right: 20px;
            `;
            
            // Create text element
            const textElement = document.createElement('span');
            textElement.textContent = item.text.toUpperCase();
            textElement.style.cssText = `
                font-family: 'Neue Haas Grotesk Display Pro', Arial, sans-serif;
                font-size: 24px;
                font-weight: 500;
                color: #ffffff;
                letter-spacing: 2px;
                text-align: right;
                transition: all 0.3s ease;
            `;
            
            menuItem.appendChild(textElement);
            
            // Add click handler
            menuItem.addEventListener('click', () => {
                console.log(`Navigating to: ${item.url}`);
                window.location.href = item.url;
            });
            
            // Add hover effects
            menuItem.addEventListener('mouseenter', () => {
                textElement.style.color = '#00ff00';
                textElement.style.textShadow = '0 0 10px rgba(0, 255, 0, 0.5)';
                textElement.style.transform = 'scale(1.05)';
            });
            
            menuItem.addEventListener('mouseleave', () => {
                textElement.style.color = '#ffffff';
                textElement.style.textShadow = 'none';
                textElement.style.transform = 'scale(1)';
            });
            
            this.container.appendChild(menuItem);
        });
    }
    
    show() {
        console.log('Showing clean menu...');
        this.container.style.opacity = '1';
    }
    
    hide() {
        this.container.style.opacity = '0';
    }
    
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// Make globally available
window.CleanMenu = CleanMenu;
