<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Build Your Own Community - Homara</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="stylesheet" href="../../sidebar.css">
    <link rel="stylesheet" href="build.css">
    <link rel="icon" href="../../homara1.png">
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button id="toggle-sidebar">MENU</button>

    <!-- Sidebar -->
    <div id="sidebar" class="hidden">
        <div class="sidebar-header">
            <h2 id="sidebar-title">Community</h2>
            <button id="close-sidebar">×</button>
        </div>
        <div class="sidebar-content">
            <!-- Top half: Navigation links -->
            <div class="sidebar-nav">
                <div class="nav-item" data-page="join">
                    <h3>APPLY TO JOIN A COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="build">
                    <h3>BUILD YOUR OWN COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="news">
                    <h3>NEWS</h3>
                </div>
                <div class="nav-item" data-page="learn">
                    <h3>INFO</h3>
                </div>
                <div class="nav-item" data-page="home">
                    <h3>BACK TO HOMARA</h3>
                </div>
            </div>

            <!-- Divider -->
            <div class="sidebar-divider"></div>

            <!-- Bottom half: Viewed points history -->
            <div class="sidebar-history">
                <h3>RECENTLY VIEWED POINTS</h3>
                <div id="points-history" class="points-history-container">
                    <!-- Points history will be populated dynamically -->
                    <div class="empty-history-message">
                        Your viewed points will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <header>
            <h1>Build Your Own Community</h1>
        </header>

        <section>
            <h2>Create Your Digital Space</h2>
            <p>
                Homara allows you to build and customize your own digital community.
                Express yourself through a unique 3D environment that you can share with others.
            </p>
            <p>
                To get started, you'll need to register for a Builder ID and download our desktop application.
            </p>
            <div id="registration-section">
                <h3>Register for a Builder ID</h3>
                <p>Create your account to get started with Homara.</p>
                <form id="registration-form">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" required>
                        <small class="form-hint">Used for login verification</small>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" required>
                        <small class="form-hint">Minimum 8 characters</small>
                    </div>
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" required>
                        <small class="form-hint">This will be your Builder ID (Homara.com/YourUsername)</small>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" required>
                        <small class="form-hint">Used for account verification and recovery</small>
                    </div>
                    <button type="submit" id="register-button">Register</button>
                </form>
            </div>

            <div id="download-section" class="hidden">
                <h3>Download Desktop Application</h3>
                <p>Thank you for registering! You can now download our desktop application to start building your community.</p>
                <p>Your Builder ID: <strong id="user-builder-id">Username</strong></p>
                <p>Your community will be available at: <strong id="user-community-url">Homara.com/Username</strong></p>
                <a href="#" class="button" id="download-button">Download Desktop App</a>
                <p class="download-instructions">
                    After downloading, install the application and log in using your email and password.
                    Your Builder ID will be automatically linked to your account.
                </p>
            </div>
        </section>

        <section>
            <h2>How It Works</h2>
            <div class="grid">
                <div class="grid-item">
                    <h3>1. Register</h3>
                    <p>Create your Builder ID and secure your unique URL at Homara.com/YourID</p>
                </div>
                <div class="grid-item">
                    <h3>2. Design</h3>
                    <p>Use our desktop application to customize your community shape and add interactive points</p>
                </div>
                <div class="grid-item">
                    <h3>3. Publish</h3>
                    <p>Sync your changes to our servers and share your community with the world</p>
                </div>
            </div>
        </section>

        <section>
            <h2>Pricing</h2>
            <p>
                Creating your own community requires a one-time purchase of a Builder ID.
                This gives you lifetime access to your unique URL and our desktop application.
            </p>
            <div class="grid">
                <div class="grid-item">
                    <h3>Basic</h3>
                    <p>$19.99</p>
                    <ul>
                        <li>1 Builder ID</li>
                        <li>Standard shapes</li>
                        <li>Up to 50 points</li>
                    </ul>
                </div>
                <div class="grid-item">
                    <h3>Premium</h3>
                    <p>$39.99</p>
                    <ul>
                        <li>1 Builder ID</li>
                        <li>All shapes</li>
                        <li>Unlimited points</li>
                        <li>Custom colors</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

    <!-- Configuration -->
    <script src="../../config.js"></script>

    <!-- Constants -->
    <script src="../../src/constants.js"></script>

    <!-- Add MongoDB Connection -->
    <script src="../../src/MongoDBConnection.js"></script>

    <!-- Add Firebase Authentication -->
    <script src="../../src/FirebaseAuth.js"></script>

    <!-- Add sidebar functionality -->
    <script src="../../src/HomaraStart/Sidebar.js"></script>
    <script>
        // Initialize sidebar and authentication
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = new Sidebar();
            const auth = new FirebaseAuth();

            // Initialize Firebase
            auth.initialize();

            // Registration form handling
            const registrationForm = document.getElementById('registration-form');
            const registrationSection = document.getElementById('registration-section');
            const downloadSection = document.getElementById('download-section');
            const userBuilderId = document.getElementById('user-builder-id');
            const userCommunityUrl = document.getElementById('user-community-url');

            registrationForm.addEventListener('submit', async function(event) {
                event.preventDefault();

                // Get form values
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const username = document.getElementById('username').value;
                const phone = document.getElementById('phone').value;

                // Validate form
                if (!validateForm(email, password, username, phone)) {
                    return;
                }

                // Show loading state
                const registerButton = document.getElementById('register-button');
                const originalButtonText = registerButton.textContent;
                registerButton.innerHTML = 'Registering... <span class="loading"></span>';
                registerButton.disabled = true;

                // Register user
                try {
                    // First, check if username is available
                    console.log(`Checking if username '${username}' is available`);

                    // Check username availability with backend
                    let usernameCheckResult = { exists: false };
                    try {
                        const usernameResponse = await fetch(`${window.API_BASE_URL}/auth/check-username?username=${encodeURIComponent(username)}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (usernameResponse.ok) {
                            usernameCheckResult = await usernameResponse.json();
                            console.log('Username check result:', usernameCheckResult);
                        } else {
                            console.log('Username check endpoint not available, proceeding with registration');
                        }
                    } catch (usernameError) {
                        console.log('Username check failed, proceeding with registration:', usernameError.message);
                    }

                    if (usernameCheckResult.exists) {
                        showMessage('Username is already taken. Please choose another one.', 'error');
                        registerButton.innerHTML = originalButtonText;
                        registerButton.disabled = false;
                        return;
                    }

                    // Add detailed logging for debugging
                    console.log('=== REGISTRATION DEBUG START ===');
                    console.log('Form data:', { email, username, phone: phone.substring(0, 3) + '***' });
                    console.log('Firebase config available:', typeof window.FIREBASE_CONFIG !== 'undefined');
                    console.log('API base URL:', window.API_BASE_URL);

                    // Register user with Firebase and MongoDB
                    const result = await auth.registerUser(email, password, username, phone);

                    console.log('Registration result:', result);
                    console.log('=== REGISTRATION DEBUG END ===');

                    if (result.success) {
                        // Show success message
                        showMessage('Registration successful! You can now download the desktop app.', 'success');

                        // Update UI with user info
                        userBuilderId.textContent = username;
                        userCommunityUrl.textContent = 'Homara.com/' + username;

                        // Hide registration form and show download section
                        registrationSection.classList.add('hidden');
                        downloadSection.classList.remove('hidden');

                        // Send verification email
                        try {
                            // The verification email is already sent by Firebase Auth during registration
                            // We don't need to make a separate API call here
                            showMessage('A verification email has been sent to your email address.', 'success');
                        } catch (verificationError) {
                            console.error('Failed to send verification email:', verificationError);
                        }
                    } else {
                        // Show error message with more details
                        const errorMessage = result.error?.message || 'Unknown error';
                        console.error('Registration error details:', JSON.stringify(result.error, null, 2));

                        // Log each property of the error object for debugging
                        if (result.error) {
                            console.log('Error properties:');
                            for (const prop in result.error) {
                                console.log(`${prop}: ${result.error[prop]}`);
                            }
                        }

                        showMessage('Registration failed: ' + errorMessage, 'error');

                        // Reset button
                        registerButton.innerHTML = originalButtonText;
                        registerButton.disabled = false;
                    }
                } catch (error) {
                    // Show error message with more details
                    console.error('Unexpected registration error:', error);

                    let errorMessage = error.message || 'An unexpected error occurred';

                    // Check for specific error types
                    if (error.code && error.code.includes('auth/')) {
                        // Firebase auth errors
                        if (error.code === 'auth/email-already-in-use') {
                            errorMessage = 'This email address is already in use. Please use a different email or try logging in.';
                        } else if (error.code === 'auth/weak-password') {
                            errorMessage = 'The password is too weak. Please use at least 6 characters.';
                        }
                    }

                    showMessage('Registration error: ' + errorMessage, 'error');

                    // Reset button
                    registerButton.innerHTML = originalButtonText;
                    registerButton.disabled = false;
                }
            });

            // Download button handling
            const downloadButton = document.getElementById('download-button');
            downloadButton.addEventListener('click', function(event) {
                event.preventDefault();

                // In a real implementation, this would trigger the download
                // For now, we'll just show a message
                alert('Desktop application download started. After installation, log in with your email and password.');

                // You could redirect to an actual download URL like this:
                // window.location.href = 'https://downloads.homara.com/desktop-app/latest';
            });

            // Form validation function
            function validateForm(email, password, username, phone) {
                // Clear previous error messages
                clearMessages();

                let isValid = true;

                // Validate email
                if (!email || !email.includes('@') || !email.includes('.')) {
                    showMessage('Please enter a valid email address.', 'error');
                    isValid = false;
                }

                // Validate password
                if (!password) {
                    showMessage('Please enter a password.', 'error');
                    isValid = false;
                } else if (password.length < 6) {
                    showMessage('Password must be at least 6 characters long.', 'error');
                    isValid = false;
                }

                // Validate username
                if (!username || username.length < 3) {
                    showMessage('Username must be at least 3 characters long.', 'error');
                    isValid = false;
                }

                // Check for special characters in username
                const usernameRegex = /^[a-zA-Z0-9_-]+$/;
                if (!usernameRegex.test(username)) {
                    showMessage('Username can only contain letters, numbers, underscores, and hyphens.', 'error');
                    isValid = false;
                }

                // Validate phone number
                const phoneRegex = /^\+?[0-9]{10,15}$/;
                if (!phone || !phoneRegex.test(phone.replace(/[\s()-]/g, ''))) {
                    showMessage('Please enter a valid phone number.', 'error');
                    isValid = false;
                }

                return isValid;
            }

            // Helper function to show messages
            function showMessage(message, type) {
                const messageElement = document.createElement('div');
                messageElement.className = type + '-message';
                messageElement.textContent = message;

                // Insert message before the form
                registrationForm.insertAdjacentElement('beforebegin', messageElement);
            }

            // Helper function to clear messages
            function clearMessages() {
                const messages = document.querySelectorAll('.success-message, .error-message');
                messages.forEach(message => message.remove());
            }

            // Set up event listeners for navigation items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    switch(page) {
                        case 'join':
                            window.location.href = '../join/index.html';
                            break;
                        case 'build':
                            window.location.href = '../build/index.html';
                            break;
                        case 'news':
                            window.location.href = '../news/index.html';
                            break;
                        case 'learn':
                            window.location.href = '../learn/index.html';
                            break;
                        case 'home':
                            window.location.href = '../../index.html';
                            break;
                    }
                });
            });
        });
    </script>
</body>
</html>
