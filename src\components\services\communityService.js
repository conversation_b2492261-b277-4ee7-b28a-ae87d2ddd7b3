/**
 * Community Service
 * Handles all operations related to community data
 */
import api from './api';

// Service for handling community data
const communityService = {
  /**
   * Get community data by ID
   * @param {string} communityId - Community ID
   * @returns {Promise<Object>} Community data
   */
  getCommunityById: async (communityId) => {
    try {
      return await api.getCommunity(communityId);
    } catch (error) {
      console.error(`Failed to get community with ID ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Get community data by username
   * @param {string} username - Username
   * @returns {Promise<Object>} Community data
   */
  getCommunityByUsername: async (username) => {
    try {
      // First get user info to find associated community
      const user = await api.getUserByUsername(username);
      if (!user || !user.communityId) {
        throw new Error('User or community not found');
      }

      // Then get the community data
      return await api.getCommunity(user.communityId);
    } catch (error) {
      console.error('Failed to get community by username:', error);
      throw error;
    }
  },

  /**
   * Get community data by builder ID
   * @param {string} builderId - Builder ID
   * @returns {Promise<Object>} Community data
   */
  getCommunityByBuilderId: async (builderId) => {
    try {
      // In this implementation, builder ID is the same as community ID
      return await api.getCommunity(builderId);
    } catch (error) {
      console.error(`Failed to get community with builder ID ${builderId}:`, error);
      throw error;
    }
  },

  /**
   * Get all points for a community
   * @param {string} communityId - Community ID
   * @returns {Promise<Array>} Array of community points
   */
  getCommunityPoints: async (communityId) => {
    try {
      return await api.getCommunityPoints(communityId);
    } catch (error) {
      console.error(`Failed to get points for community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new community
   * @param {Object} communityData - Community data
   * @returns {Promise<Object>} Created community
   */
  createCommunity: async (communityData) => {
    try {
      return await api.createCommunity(communityData);
    } catch (error) {
      console.error('Failed to create community:', error);
      throw error;
    }
  },

  /**
   * Update community data
   * @param {string} communityId - Community ID
   * @param {Object} communityData - Updated community data
   * @returns {Promise<Object>} Updated community
   */
  updateCommunity: async (communityId, communityData) => {
    try {
      return await api.updateCommunity(communityId, communityData);
    } catch (error) {
      console.error(`Failed to update community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Search for communities
   * @param {string} query - Search query
   * @returns {Promise<Array>} Array of matching communities
   */
  searchCommunities: async (query) => {
    try {
      return await api.searchCommunities(query);
    } catch (error) {
      console.error('Failed to search communities:', error);
      throw error;
    }
  },

  /**
   * Get community shape template
   * @param {string} communityId - Community ID
   * @returns {Promise<Object>} Community shape template
   */
  getCommunityShape: async (communityId) => {
    try {
      const community = await api.getCommunity(communityId);
      return community.communityShape || 'tree'; // Default to tree if not specified
    } catch (error) {
      console.error(`Failed to get shape for community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Update community shape
   * @param {string} communityId - Community ID
   * @param {string} shapeType - Shape type (e.g., 'tree')
   * @returns {Promise<Object>} Updated community
   */
  updateCommunityShape: async (communityId, shapeType) => {
    try {
      return await api.updateCommunity(communityId, { communityShape: shapeType });
    } catch (error) {
      console.error(`Failed to update shape for community ${communityId}:`, error);
      throw error;
    }
  }
};

export default communityService;