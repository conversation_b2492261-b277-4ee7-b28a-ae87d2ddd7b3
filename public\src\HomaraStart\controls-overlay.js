class ControlsOverlay {
    constructor() {
        this.overlay = document.getElementById('controls-overlay');
        this.hideTimeout = null;
    }

    show(duration = 8000) {
        // Clear any existing timeout
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
        }

        // Show the overlay
        this.overlay.classList.remove('hidden');
        this.overlay.classList.add('visible');

        // Set timeout to hide after duration
        this.hideTimeout = setTimeout(() => {
            this.hide();
        }, duration);
    }

    hide() {
        // Remove visible class first (triggers fade out)
        this.overlay.classList.remove('visible');

        // After fade out completes, add hidden class
        setTimeout(() => {
            this.overlay.classList.add('hidden');
        }, 500); // Match this to the CSS transition duration
    }

    toggle(duration = 5000) {
        if (this.overlay.classList.contains('hidden')) {
            this.show(duration);
        } else {
            this.hide();
        }
    }
}

// Export the ControlsOverlay class
window.ControlsOverlay = ControlsOverlay;
