class Sidebar {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.closeButton = document.getElementById('close-sidebar');
        this.sidebarTitle = document.getElementById('sidebar-title');
        this.navItems = document.querySelectorAll('.nav-item');
        this.pointsHistory = document.getElementById('points-history');

        // Track viewed points (will be populated from database in the future)
        this.viewedPoints = [];

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Close sidebar when close button is clicked
        this.closeButton.addEventListener('click', () => {
            this.hide();
        });

        // Close sidebar when clicking outside of it
        document.addEventListener('click', (event) => {
            // Check if click is outside the sidebar
            if (this.isVisible() &&
                !this.sidebar.contains(event.target) &&
                event.target.id !== 'toggle-sidebar') {
                this.hide();
            }
        });

        // Add click event listeners to navigation items
        this.navItems.forEach(item => {
            item.addEventListener('click', () => {
                const page = item.getAttribute('data-page');
                this.navigateTo(page);
            });
        });
    }

    // Navigate to different pages
    navigateTo(page) {
        console.log(`Navigating to ${page} page`);

        // Navigate to the HTML pages
        switch(page) {
            case 'build':
                window.open('/Menu/build/index.html', '_blank');
                break;
            case 'join':
                window.open('/Menu/join/index.html', '_blank');
                break;
            case 'learn':
                window.open('/Menu/learn/index.html', '_blank');
                break;
            case 'news':
                window.open('/Menu/news/index.html', '_blank');
                break;
            case 'home':
                window.location.href = '/index.html';
                break;
            default:
                console.error('Unknown page:', page);
        }
    }

    show() {
        this.sidebar.classList.remove('hidden');
        // Use setTimeout to ensure the transition happens after the display change
        setTimeout(() => {
            this.sidebar.classList.add('visible');
        }, 10);
    }

    hide() {
        this.sidebar.classList.remove('visible');
        // Wait for transition to complete before hiding
        setTimeout(() => {
            this.sidebar.classList.add('hidden');
        }, 300); // Match this to the CSS transition duration
    }

    toggle() {
        if (this.isVisible()) {
            this.hide();
        } else {
            this.show();
        }
    }

    isVisible() {
        return !this.sidebar.classList.contains('hidden');
    }

    updateContent(name) {
        // Update the sidebar title to show the username's community
        this.sidebarTitle.textContent = name || 'Community';
    }

    // Add a point to the viewed history
    addPointToHistory(point) {
        // In the future, this will be stored in a database
        // For now, just add to the local array

        // Check if point already exists in history
        const existingIndex = this.viewedPoints.findIndex(p =>
            p.id === point.id ||
            (p.position.x === point.position.x &&
             p.position.y === point.position.y &&
             p.position.z === point.position.z)
        );

        if (existingIndex !== -1) {
            // Remove the existing point (we'll add it to the top)
            this.viewedPoints.splice(existingIndex, 1);
        }

        // Add the new point to the beginning of the array
        this.viewedPoints.unshift({
            id: point.id || `point-${Date.now()}`,
            title: point.content || `Point at (${point.position.x.toFixed(1)}, ${point.position.y.toFixed(1)}, ${point.position.z.toFixed(1)})`,
            author: point.author || 'Unknown',
            position: point.position,
            timestamp: new Date().toISOString()
        });

        // Limit history to 10 items
        if (this.viewedPoints.length > 10) {
            this.viewedPoints.pop();
        }

        // Update the UI
        this.updatePointsHistoryUI();
    }

    // Update the points history UI
    updatePointsHistoryUI() {
        // Clear the current content
        this.pointsHistory.innerHTML = '';

        // If no points, show empty message
        if (this.viewedPoints.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-history-message';
            emptyMessage.textContent = 'Your viewed points will appear here';
            this.pointsHistory.appendChild(emptyMessage);
            return;
        }

        // Add each point to the UI
        this.viewedPoints.forEach(point => {
            const pointElement = document.createElement('div');
            pointElement.className = 'point-history-item';

            // Create preview
            const preview = document.createElement('div');
            preview.className = 'point-preview';

            const previewPlaceholder = document.createElement('div');
            previewPlaceholder.className = 'point-preview-placeholder';
            previewPlaceholder.textContent = 'Point Preview';

            preview.appendChild(previewPlaceholder);

            // Create info section
            const info = document.createElement('div');
            info.className = 'point-info';

            const title = document.createElement('div');
            title.className = 'point-title';
            title.textContent = point.title;

            const author = document.createElement('div');
            author.className = 'point-author';
            author.textContent = point.author;

            info.appendChild(title);
            info.appendChild(author);

            // Add elements to point item
            pointElement.appendChild(preview);
            pointElement.appendChild(info);

            // Add click handler
            pointElement.addEventListener('click', () => {
                console.log('Clicked on history point:', point);
                // In the future, this will navigate to the point in the 3D environment
            });

            // Add to container
            this.pointsHistory.appendChild(pointElement);
        });
    }
}

// Export the Sidebar class
window.Sidebar = Sidebar;
