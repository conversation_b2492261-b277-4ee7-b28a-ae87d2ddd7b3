// THREE and OBJLoader are loaded via script tags


///Enviroment loader
/// going to need this database information:     
/// FIRST from communities database - CommunityId, CommunityShape, List of pointIDS
/// SECOND users database (loads from PointId) - all usernames and member types 
/// THIRD points database( loads fro PointIds) - PointPositions and metadata for telnyx content loading + preview frames for the hover animation. 



/// Create Account:
/// First Ask people for :   email, password, username, phone number, referral code
/// Creates: Firebase records (returns firebaseUID), mongo DB records: email, PointId , New CommunityId (IF NO REFERRAL CODE), 
/// data limit, member type , 


///Payments Tab:  - checks member types 
/// First checks founder + member or inactive    - in the top right you can deactivate your account to not pay
/// If founder display full referral code management system - displays community name +list of all usernames and 
/// able to change amount due for each one 

/// If member display the community dues system , shows what communities PointId is in + amount and community Name 
/// as well as the payment date, for your communitiy payments  - needs to integrate with stripe for data transfer

/// for both display Data management system : shows HP bar of data available + data used and total data
/// allows you to change the amount of data you would like to purchse when you exceed the limit





class EnvLoader {
    constructor() {
        // Environment properties
        this.builderID = null;
        this.communityShape = null;
        this.communityPoints = [];
        this.shapeInstance = null;

        // Group to hold all environment elements
        this.group = new THREE.Group();
        
        // Initialize MongoDB connection
        this.mongoDBConnection = new MongoDBConnection();
    }

    // Load environment data from MongoDB
    async loadEnvironment(builderID) {
        this.builderID = builderID || 'default';
        console.log(`Loading environment for builder ID: ${this.builderID}`);

        try {
            // Connect to MongoDB and fetch environment data
            await this.mongoDBConnection.connect();
            const result = await this.mongoDBConnection.getCommunityByBuilderID(this.builderID);
            
            if (!result.success) {
                throw new Error('Failed to fetch community data');
            }
            
            const environmentData = result.community;

            // Set community shape from database
            this.communityShape = environmentData.communityShape;
            console.log(`Community shape: ${this.communityShape}`);

            // Load the appropriate shape template
            await this.loadShapeTemplate();

            // Load community points
            await this.loadCommunityPoints();

            return this.group;
        } catch (error) {
            console.error('Error loading environment:', error);
            // Load fallback environment
            this.loadFallbackEnvironment();
            return this.group;
        }
    }

    // Load the appropriate shape template based on communityShape
    async loadShapeTemplate() {
        console.log(`Loading shape template: ${this.communityShape}`);

        try {
            // This would dynamically import the shape module
            // For now, we'll use a switch statement to handle different shapes
            switch (this.communityShape) {
                case 'tree':
                    // Import the TreePointCloud class
                    // In a real implementation, this would be a dynamic import
                    // For now, we'll assume TreePointCloud is globally available

                    // Create tree shape instance
                    this.shapeInstance = new TreePointCloud({
                        height: 40,
                        radiusBase: 2,
                        branchLevels: 5,
                        pointsPerLevel: 2000,
                        colorVariation: 0.3,
                        baseColor: 0x228B22,
                        modelPath: 'models/tree.obj',
                        pointSize: 0.2
                    });
                    
                    // Position the tree in its original position
                    this.shapeInstance.points.position.y = 0; // Original position
                    
                    // Add shape to the environment group
                    this.group.add(this.shapeInstance.points);
                    break;

                // Add cases for other shapes here
                default:
                    console.warn(`Unknown shape: ${this.communityShape}, loading fallback`);
                    this.loadFallbackEnvironment();
                    break;
            }
        } catch (error) {
            console.error('Error loading shape template:', error);
            this.loadFallbackEnvironment();
        }
    }

    // Load community points from database
    async loadCommunityPoints() {
        console.log('Loading community points');

        try {
            // Fetch community points from MongoDB
            const result = await this.mongoDBConnection.getCommunityByBuilderID(this.builderID);
            
            if (!result.success) {
                throw new Error('Failed to fetch community points');
            }
            
            const pointsData = result.community.points || [];

            // Create points based on database data
            this.createPointsFromData(pointsData);
        } catch (error) {
            console.error('Error loading community points:', error);
            // Create some default points
            this.createDefaultPoints();
        }
    }

    // Create points from database data
    createPointsFromData(pointsData) {
        console.log(`Creating ${pointsData.length} community points`);

        // Store the points data
        this.communityPoints = pointsData;

        // If we have a shape instance with a method to set custom points, use it
        if (this.shapeInstance && typeof this.shapeInstance.setCustomPoints === 'function') {
            this.shapeInstance.setCustomPoints(pointsData);
        } else if (this.shapeInstance) {
            // If the shape doesn't have a setCustomPoints method, we'll update its customPointPositions
            // and recreate the interactive points
            this.shapeInstance.params.customPointPositions = pointsData;

            // Remove existing interactive points
            this.shapeInstance.points.traverse(child => {
                if (child instanceof THREE.Points && child.name === "interactivePoints") {
                    this.shapeInstance.points.remove(child);
                }
            });

            // Recreate interactive points
            this.shapeInstance.createInteractivePoints();
        }
    }

    // Create default points if database fetch fails
    createDefaultPoints() {
        const defaultPoints = [
            { x: 5, y: 10, z: 0, content: 'Default Point 1' },
            { x: -5, y: 15, z: 3, content: 'Default Point 2' },
            { x: 0, y: 20, z: -5, content: 'Default Point 3' }
        ];

        this.createPointsFromData(defaultPoints);
    }

    // Load a fallback environment if everything else fails
    loadFallbackEnvironment() {
        console.log('Loading fallback environment');

        // Create a simple environment with a basic shape
        this.shapeInstance = new TreePointCloud({
            height: 30,
            radiusBase: 1.5,
            branchLevels: 3,
            pointsPerLevel: 1000,
            colorVariation: 0.2,
            baseColor: 0x00ff00,
            pointSize: 0.1
        });

        // Position the tree in its original position
        this.shapeInstance.points.position.y = 0; // Original position

        // Add shape to the environment group
        this.group.add(this.shapeInstance.points);

        // Create some default points
        this.createDefaultPoints();
    }

    // Update method to be called in animation loop
    update(camera) {
        // Update shape instance if it exists
        if (this.shapeInstance && typeof this.shapeInstance.update === 'function') {
            this.shapeInstance.update(camera);
        }
    }
}

// Export the EnvLoader class
window.EnvLoader = EnvLoader;
