/**
 * API Service
 * Centralized service for all API calls to the backend
 */

// Base API URL from environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Import Firebase auth functions
import { getAuth } from 'firebase/auth';
import { app } from '../firebase/config';

/**
 * Get the current Firebase user
 * @returns {Object|null} Firebase user object or null if not logged in
 */
const getCurrentUser = () => {
  const auth = getAuth(app);
  return auth.currentUser;
};

/**
 * Get Firebase ID token for the current user
 * @returns {Promise<string|null>} ID token or null if not logged in
 */
const getIdToken = async () => {
  const user = getCurrentUser();
  if (!user) return null;
  
  try {
    return await user.getIdToken(true);
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
};

/**
 * Make an authenticated API request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response
 */
const makeAuthenticatedRequest = async (endpoint, options = {}) => {
  // Get the current user's ID token from Firebase
  const user = getCurrentUser();
  let idToken = '';
  
  if (user) {
    idToken = await getIdToken();
  } else {
    throw new Error('User not authenticated');
  }
  
  // Set up headers with the token
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${idToken}`,
    ...options.headers
  };
  
  // Make the request
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `API error: ${response.status}`);
  }
  
  return await response.json();
};

/**
 * Safe API call wrapper with error handling
 * @param {Function} apiFunction - API function to call
 * @param {...any} args - Arguments to pass to the API function
 * @returns {Promise<Object>} API response or error
 */
const safeApiCall = async (apiFunction, ...args) => {
  try {
    return await apiFunction(...args);
  } catch (error) {
    console.error('API call failed:', error);
    // Handle error appropriately (show notification, redirect, etc.)
    throw error;
  }
};

// Authentication service
const authService = {
  // Register a new user
  register: async (userData) => {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Registration failed');
    }
    
    return await response.json();
  },
  
  // Check if username is available
  checkUsername: async (username) => {
    const response = await fetch(`${API_BASE_URL}/auth/check-username?username=${encodeURIComponent(username)}`);
    return await response.json();
  },
  
  // Send verification email
  sendVerification: async (email) => {
    const response = await fetch(`${API_BASE_URL}/auth/send-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to send verification email');
    }
    
    return await response.json();
  }
};

// User service
const userService = {
  // Get user profile
  getProfile: async () => {
    return await makeAuthenticatedRequest('/user/profile', { method: 'GET' });
  },
  
  // Update user profile
  updateProfile: async (profileData) => {
    return await makeAuthenticatedRequest('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData)
    });
  }
};

// Test backend connection
const testBackendConnection = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    console.log('Backend connection successful:', data);
    return true;
  } catch (error) {
    console.error('Backend connection failed:', error);
    return false;
  }
};

// Export the API services
export {
  API_BASE_URL,
  getCurrentUser,
  getIdToken,
  makeAuthenticatedRequest,
  safeApiCall,
  authService,
  userService,
  testBackendConnection
};

export default {
  auth: authService,
  user: userService,
  testConnection: testBackendConnection
};
