/**
 * Authentication Context
 * Provides Firebase authentication state and methods to the application
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  getAuth, 
  onAuthStateChanged, 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  sendEmailVerification
} from 'firebase/auth';
import { app } from '../firebase/config';
import { createUser } from '../api/userApi';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Authentication provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const auth = getAuth(app);

  // Register a new user with both Firebase and MongoDB
  const register = async (email, password, username, displayName) => {
    try {
      // First create the user in Firebase
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update the user's profile with the display name
      await updateProfile(userCredential.user, { displayName: displayName || username });
      
      // Send email verification
      await sendEmailVerification(userCredential.user);
      
      // Then create the user in MongoDB through our API
      const userData = {
        email,
        username,
        displayName: displayName || username,
        firebaseUid: userCredential.user.uid
      };
      
      await createUser(userData);
      
      return userCredential.user;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  // Sign in a user
  const login = async (email, password) => {
    return signInWithEmailAndPassword(auth, email, password);
  };

  // Sign out a user
  const logout = () => {
    return signOut(auth);
  };

  // Get the current user's ID token
  const getIdToken = async () => {
    if (!currentUser) return null;
    try {
      return await currentUser.getIdToken(true);
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  };

  // Effect to handle auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, [auth]);

  // Context value
  const value = {
    currentUser,
    register,
    login,
    logout,
    getIdToken
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
