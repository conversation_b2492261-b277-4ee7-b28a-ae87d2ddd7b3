// THREE is loaded via script tags

class Environment {
    constructor() {
        this.group = new THREE.Group();
        this.createLighting();
    }

    // Ground and grid removed to create a void effect

    createLighting() {
        // Ambient light for overall illumination - slightly brighter to make tree visible in void
        const ambientLight = new THREE.AmbientLight(0x404040, 0.7);
        this.group.add(ambientLight);

        // Directional light to highlight the tree
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;

        // Configure shadow properties
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;

        this.group.add(directionalLight);

        // Add a second directional light from the opposite side for better illumination
        const backLight = new THREE.DirectionalLight(0xffffff, 0.5);
        backLight.position.set(-50, 80, -50);
        this.group.add(backLight);

        // Add a subtle point light near the center of the tree for better visibility
        const centerLight = new THREE.PointLight(0xffffff, 0.5, 100);
        centerLight.position.set(0, 20, 0);
        this.group.add(centerLight);
    }
}

// Export the Environment class
window.Environment = Environment;
