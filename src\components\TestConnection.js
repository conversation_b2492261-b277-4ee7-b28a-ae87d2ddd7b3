import React, { useState, useEffect } from 'react';
import { testBackendConnection } from '../api/userApi';

const TestConnection = () => {
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const isConnected = await testBackendConnection();
        setConnectionStatus(isConnected ? 'connected' : 'failed');
      } catch (err) {
        setConnectionStatus('failed');
        setError(err.message);
      }
    };

    checkConnection();
  }, []);

  const handleRetry = async () => {
    setConnectionStatus('checking');
    setError(null);
    
    try {
      const isConnected = await testBackendConnection();
      setConnectionStatus(isConnected ? 'connected' : 'failed');
    } catch (err) {
      setConnectionStatus('failed');
      setError(err.message);
    }
  };

  return (
    <div className="test-connection">
      <h2>Backend Connection Test</h2>
      
      <div className="connection-status">
        {connectionStatus === 'checking' && (
          <div className="status checking">
            <span className="spinner"></span>
            Checking connection to backend...
          </div>
        )}
        
        {connectionStatus === 'connected' && (
          <div className="status connected">
            <span className="icon">✅</span>
            Connected to backend successfully!
          </div>
        )}
        
        {connectionStatus === 'failed' && (
          <div className="status failed">
            <span className="icon">❌</span>
            Failed to connect to backend.
            {error && <div className="error-message">{error}</div>}
            <button onClick={handleRetry} className="retry-button">
              Retry Connection
            </button>
          </div>
        )}
      </div>
      
      <div className="connection-info">
        <h3>Connection Details</h3>
        <p>API URL: {process.env.REACT_APP_API_URL || 'http://localhost:3000/api'}</p>
        <p>Environment: {process.env.NODE_ENV}</p>
      </div>
    </div>
  );
};

export default TestConnection;
