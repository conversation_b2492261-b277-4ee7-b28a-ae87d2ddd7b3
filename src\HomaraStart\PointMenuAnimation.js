/**
 * Point Menu Animation System
 * Handles the flying point animation that forms text, then transitions to clean menu
 */

class PointMenuAnimation {
    constructor(onComplete) {
        this.onComplete = onComplete;
        this.container = null;
        this.menuItems = [
            { text: 'JOIN HOMARA', url: 'dev-menu/join-homara/index.html' },
            { text: 'WHY', url: 'dev-menu/why/index.html' },
            { text: 'OUR TIMELINE', url: 'dev-menu/timeline/index.html' }
        ];
        this.pointSets = [];
        this.isAnimating = false;
        this.animationStartTime = null;
        
        this.init();
    }
    
    init() {
        // Create the animation container
        this.container = document.createElement('div');
        this.container.id = 'point-menu-animation-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            pointer-events: none;
            width: 500px;
            height: 400px;
            background-color: rgba(255, 0, 0, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
        `;
        document.body.appendChild(this.container);
        
        // Wait for fonts to load, then create text templates
        setTimeout(() => {
            this.createTextTemplates();
        }, 100);
    }
    
    createTextTemplates() {
        console.log('PointMenuAnimation: Creating text templates for', this.menuItems.length, 'items');

        this.menuItems.forEach((item, index) => {
            console.log(`PointMenuAnimation: Processing item ${index}: "${item.text}"`);

            const canvas = document.createElement('canvas');
            canvas.width = 500;
            canvas.height = 80;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ffffff';
            // Use a simpler, more reliable font first
            ctx.font = 'bold 24px Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.textBaseline = 'middle';

            // Clear canvas first
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw text
            const text = item.text.toUpperCase();
            ctx.fillText(text, 480, 40);

            console.log(`PointMenuAnimation: Drew text "${text}" on canvas ${canvas.width}x${canvas.height}`);

            // Extract points from the text
            const points = this.extractPointsFromCanvas(ctx, canvas, index);
            console.log(`PointMenuAnimation: Extracted ${points.length} points for "${item.text}"`);

            this.pointSets.push({
                points: points,
                text: item.text,
                url: item.url,
                yOffset: index * 100 // More spacing for larger text
            });
        });

        console.log('PointMenuAnimation: Total point sets created:', this.pointSets.length);
    }
    
    extractPointsFromCanvas(ctx, canvas, menuIndex) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const points = [];
        const density = 2; // Sample every 2nd pixel

        console.log(`PointMenuAnimation: Extracting points from ${canvas.width}x${canvas.height} canvas`);

        let totalPixels = 0;
        let visiblePixels = 0;

        for (let y = 0; y < canvas.height; y += density) {
            for (let x = 0; x < canvas.width; x += density) {
                const index = (y * canvas.width + x) * 4;
                const alpha = imageData.data[index + 3];
                totalPixels++;

                if (alpha > 128) { // If pixel is visible
                    visiblePixels++;
                    // Start points from behind the screen (far away) - more dramatic
                    const angle = Math.random() * Math.PI * 2;
                    const distance = 400 + Math.random() * 300; // Start 400-700px away
                    const depth = Math.random() * 150; // Simulate more depth

                    points.push({
                        targetX: x,
                        targetY: y,
                        currentX: x + Math.cos(angle) * distance,
                        currentY: y + Math.sin(angle) * distance,
                        currentZ: depth, // Depth for 3D effect
                        element: null,
                        delay: Math.random() * 2000 + menuIndex * 1000, // More stagger between menu items
                        speed: 0.04 + Math.random() * 0.03, // Slightly slower for more dramatic effect
                        hasStarted: false,
                        opacity: 0
                    });
                }
            }
        }

        console.log(`PointMenuAnimation: Scanned ${totalPixels} pixels, found ${visiblePixels} visible pixels, created ${points.length} points`);

        // Fallback: if no points found, create some test points
        if (points.length === 0) {
            console.log('PointMenuAnimation: No points found, creating fallback test points');
            for (let i = 0; i < 50; i++) {
                points.push({
                    targetX: 300 + i * 3,
                    targetY: 40,
                    currentX: Math.random() * 1000 - 500,
                    currentY: Math.random() * 1000 - 500,
                    currentZ: Math.random() * 150,
                    element: null,
                    delay: Math.random() * 2000 + menuIndex * 1000,
                    speed: 0.04 + Math.random() * 0.03,
                    hasStarted: false,
                    opacity: 0
                });
            }
        }

        return points;
    }
    
    startAnimation() {
        if (this.isAnimating) {
            console.log('PointMenuAnimation: Already animating, returning...');
            return;
        }
        this.isAnimating = true;

        console.log('PointMenuAnimation: Starting point menu formation animation...');
        console.log('PointMenuAnimation: Point sets created:', this.pointSets.length);
        
        // Create DOM elements for each point
        this.pointSets.forEach((pointSet, setIndex) => {
            console.log(`PointMenuAnimation: Creating ${pointSet.points.length} DOM elements for set ${setIndex}`);

            pointSet.points.forEach((point, pointIndex) => {
                const pointElement = document.createElement('div');
                pointElement.style.cssText = `
                    position: absolute;
                    width: 3px;
                    height: 3px;
                    background-color: #ffffff;
                    border-radius: 50%;
                    opacity: 0;
                    pointer-events: none;
                    box-shadow: 0 0 6px rgba(255, 255, 255, 0.9), 0 0 12px rgba(255, 255, 255, 0.5);
                    transition: all 0.1s ease;
                    z-index: 1001;
                `;

                point.element = pointElement;
                this.container.appendChild(pointElement);

                // Position initially (far away)
                pointElement.style.left = point.currentX + 'px';
                pointElement.style.top = (point.currentY + pointSet.yOffset) + 'px';

                // Debug: Make first few points visible immediately for testing
                if (pointIndex < 5) {
                    pointElement.style.opacity = '1';
                    pointElement.style.backgroundColor = '#ff0000'; // Red for debugging
                    console.log(`Debug point ${pointIndex} at:`, point.currentX, point.currentY + pointSet.yOffset);
                }
            });
        });
        
        // Store animation start time
        this.animationStartTime = Date.now();

        console.log(`PointMenuAnimation: Created ${this.container.children.length} point elements total`);
        console.log('PointMenuAnimation: Starting animation loop...');

        // Start the animation loop
        this.animatePoints();
    }
    
    animatePoints() {
        const currentTime = Date.now();
        const elapsedTime = currentTime - this.animationStartTime;

        // Debug logging every 1000ms
        if (elapsedTime % 1000 < 50) {
            console.log(`PointMenuAnimation: Animation running, elapsed: ${elapsedTime}ms`);
        }

        let pointsStillMoving = 0;
        let pointsWaitingToStart = 0;

        this.pointSets.forEach((pointSet, setIndex) => {
            pointSet.points.forEach((point) => {
                if (!point.element) return;

                // Check if this point should start animating
                if (elapsedTime < point.delay) {
                    pointsWaitingToStart++;
                    return;
                }
                
                // Start the point animation
                if (!point.hasStarted) {
                    point.hasStarted = true;
                    point.opacity = 1;
                }
                
                // Fade in effect as points get closer
                const dx = point.targetX - point.currentX;
                const dy = point.targetY - point.currentY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                // Calculate opacity based on distance (closer = more visible)
                const maxDistance = 500;
                const normalizedDistance = Math.min(distance / maxDistance, 1);
                const opacity = Math.max(0.1, 1 - normalizedDistance * 0.7);
                
                point.element.style.opacity = opacity;
                
                if (distance > 1) {
                    pointsStillMoving++;

                    // Move towards target with easing
                    point.currentX += dx * point.speed;
                    point.currentY += dy * point.speed;

                    // Simulate depth effect by scaling
                    const scale = Math.max(0.5, 1 - (distance / 500));
                    point.element.style.transform = `scale(${scale})`;

                    point.element.style.left = point.currentX + 'px';
                    point.element.style.top = (point.currentY + pointSet.yOffset) + 'px';
                } else {
                    // Snap to final position
                    point.currentX = point.targetX;
                    point.currentY = point.targetY;
                    point.element.style.left = point.currentX + 'px';
                    point.element.style.top = (point.currentY + pointSet.yOffset) + 'px';
                    point.element.style.transform = 'scale(1)';
                    point.element.style.opacity = '1';
                }
            });
        });

        console.log(`PointMenuAnimation: ${pointsStillMoving} points moving, ${pointsWaitingToStart} points waiting`);

        if (pointsStillMoving > 0 || pointsWaitingToStart > 0) {
            requestAnimationFrame(() => this.animatePoints());
        } else {
            console.log('Point formation animation complete');
            // Wait a moment to show the formed text, then transition
            setTimeout(() => {
                this.transitionToCleanMenu();
            }, 1000);
        }
    }
    
    transitionToCleanMenu() {
        console.log('Transitioning to clean menu...');
        
        // Fade out all points
        this.pointSets.forEach(pointSet => {
            pointSet.points.forEach(point => {
                if (point.element) {
                    point.element.style.transition = 'opacity 0.5s ease';
                    point.element.style.opacity = '0';
                }
            });
        });
        
        // After fade out, remove the animation container and call completion callback
        setTimeout(() => {
            this.destroy();
            if (this.onComplete) {
                this.onComplete();
            }
        }, 500);
    }
    
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}

// Make globally available
window.PointMenuAnimation = PointMenuAnimation;
