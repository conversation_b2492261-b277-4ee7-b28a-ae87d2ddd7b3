// Firebase Authentication Module for Homara
import { initializeApp } from 'firebase/app';
import {
    getAuth,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    signOut as firebaseSignOut,
    onAuthStateChanged,
    updateProfile,
    sendEmailVerification
} from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import { FIREBASE_CONFIG } from '../utils/constants';

class FirebaseAuth {
    constructor() {
        this.isInitialized = false;
        this.user = null;
        this.auth = null;
        this.db = null;

        // Get Firebase config from environment variables
        this.firebaseConfig = FIREBASE_CONFIG;

        // Initialize event listeners
        this.eventListeners = {
            'auth-state-changed': [],
            'registration-success': [],
            'registration-error': [],
            'login-success': [],
            'login-error': []
        };

        // Initialize MongoDB connection for user data
        this.mongoDBConnection = new MongoDBConnection();
    }

    // Initialize Firebase with the provided configuration
    initialize() {
        if (this.isInitialized) return;

        try {
            console.log('Initializing Firebase with config');

            // Initialize Firebase
            const app = initializeApp(this.firebaseConfig);

            // Get Auth and Firestore instances
            this.auth = getAuth(app);
            this.db = getFirestore(app);

            this.isInitialized = true;
            console.log('Firebase initialized successfully');

            // Set up auth state listener
            this.setupAuthStateListener();

            return true;
        } catch (error) {
            console.error('Error initializing Firebase:', error);
            return false;
        }
    }

    // Set up authentication state listener
    setupAuthStateListener() {
        onAuthStateChanged(this.auth, (user) => {
            this.user = user;
            this.triggerEvent('auth-state-changed', user);
            console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
        });

        console.log('Auth state listener set up');
    }

    // Register a new user with email, password, username, and phone number
    async registerUser(email, password, username, phoneNumber) {
        if (!this.isInitialized) this.initialize();

        try {
            console.log(`Registering user with email: ${email}, username: ${username}, and phone: ${phoneNumber}`);

            // Create user with Firebase Authentication
            const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;

            // Update profile with display name
            await updateProfile(user, { displayName: username });

            // Send email verification
            await sendEmailVerification(user);

            // Store the username and phone number in the user profile or database
            const userData = {
                userId: user.uid,
                username: username,
                email: email,
                displayName: username,
                phoneNumber: phoneNumber,
                createdAt: new Date(),
                builderID: username, // Use username as builderID
                pointPosition: { x: 0, y: 0, z: 0 }, // Default position
                profile: {
                    bio: '',
                    avatarUrl: '',
                    preferences: {
                        theme: 'system',
                        notifications: true
                    }
                }
            };

            // Save to MongoDB through our API
            const dbResult = await this.storeUserData(user.uid, username, phoneNumber, userData);

            if (!dbResult.success) {
                throw new Error('Failed to store user data in database');
            }

            this.triggerEvent('registration-success', { user, username, phoneNumber });
            return { success: true, user, username, phoneNumber };
        } catch (error) {
            console.error('Error registering user:', error);
            this.triggerEvent('registration-error', error);
            return { success: false, error };
        }
    }

    // Store additional user data in the database
    async storeUserData(userId, username, phoneNumber, fullUserData = null) {
        try {
            console.log(`Storing user data for ${userId} with username: ${username} and phone: ${phoneNumber}`);

            // Use provided userData or create a basic one
            const userData = fullUserData || {
                userId: userId,
                username: username,
                phoneNumber: phoneNumber,
                createdAt: new Date(),
                builderID: username // Use username as builderID
            };

            // Save to MongoDB
            const mongoResult = await this.mongoDBConnection.saveUser(userData);

            // Store in Firestore
            await setDoc(doc(this.db, 'users', userId), userData);

            console.log('User data stored successfully');
            return mongoResult;
        } catch (error) {
            console.error('Error storing user data:', error);
            return { success: false, error: error.message };
        }
    }

    // Sign in an existing user
    async signIn(email, password) {
        if (!this.isInitialized) this.initialize();

        try {
            console.log(`Signing in user with email: ${email}`);

            // Sign in with Firebase Authentication
            const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;

            this.user = user;
            this.triggerEvent('login-success', user);
            return { success: true, user };
        } catch (error) {
            console.error('Error signing in:', error);
            this.triggerEvent('login-error', error);
            return { success: false, error };
        }
    }

    // Sign out the current user
    async signOut() {
        if (!this.isInitialized) return { success: false, error: 'Firebase not initialized' };

        try {
            // Sign out with Firebase Authentication
            await firebaseSignOut(this.auth);

            this.user = null;
            console.log('User signed out successfully');
            return { success: true };
        } catch (error) {
            console.error('Error signing out:', error);
            return { success: false, error };
        }
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.user !== null;
    }

    // Get current user
    getCurrentUser() {
        return this.user;
    }

    // Get ID token for the current user
    async getIdToken() {
        if (!this.user) return null;

        try {
            return await this.user.getIdToken();
        } catch (error) {
            console.error('Error getting ID token:', error);
            return null;
        }
    }

    // Event handling methods
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }

    removeEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
        }
    }

    triggerEvent(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => callback(data));
        }
    }
}

// Export the FirebaseAuth class
window.FirebaseAuth = FirebaseAuth;
