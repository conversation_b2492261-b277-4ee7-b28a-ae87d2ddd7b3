/**
 * Three.js Utility Functions
 * Helper functions for working with Three.js
 */

/**
 * Create a basic Three.js scene
 * @returns {THREE.Scene} New scene with basic setup
 */
export const createScene = () => {
  const scene = new THREE.Scene();
  scene.background = new THREE.Color(0x000000); // Black background
  return scene;
};

/**
 * Create a perspective camera with default settings
 * @param {Object} options - Camera options
 * @returns {THREE.PerspectiveCamera} New camera
 */
export const createCamera = (options = {}) => {
  const {
    fov = 75,
    aspect = window.innerWidth / window.innerHeight,
    near = 0.1,
    far = 1000,
    position = { x: 0, y: 70, z: 100 },
    lookAt = { x: 0, y: 25, z: 0 }
  } = options;
  
  const camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
  camera.position.set(position.x, position.y, position.z);
  camera.lookAt(new THREE.Vector3(lookAt.x, lookAt.y, lookAt.z));
  
  return camera;
};

/**
 * Create a WebGL renderer with default settings
 * @param {Object} options - Renderer options
 * @returns {THREE.WebGLRenderer} New renderer
 */
export const createRenderer = (options = {}) => {
  const {
    antialias = true,
    alpha = true,
    width = window.innerWidth,
    height = window.innerHeight
  } = options;
  
  const renderer = new THREE.WebGLRenderer({ antialias, alpha });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);
  
  return renderer;
};

/**
 * Handle window resize for Three.js
 * @param {THREE.PerspectiveCamera} camera - Camera to update
 * @param {THREE.WebGLRenderer} renderer - Renderer to update
 */
export const handleResize = (camera, renderer) => {
  window.addEventListener('resize', () => {
    // Update camera aspect ratio
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    
    // Update renderer size
    renderer.setSize(window.innerWidth, window.innerHeight);
  });
};

/**
 * Create a point cloud from an array of points
 * @param {Array} points - Array of points with x, y, z coordinates
 * @param {Object} options - Point cloud options
 * @returns {THREE.Points} Point cloud object
 */
export const createPointCloud = (points, options = {}) => {
  const {
    size = 0.2,
    color = 0xffffff,
    colorVariation = 0.2
  } = options;
  
  // Create geometry
  const geometry = new THREE.BufferGeometry();
  
  // Create position array
  const positions = new Float32Array(points.length * 3);
  const colors = new Float32Array(points.length * 3);
  
  // Fill position and color arrays
  points.forEach((point, i) => {
    const i3 = i * 3;
    
    // Set positions
    positions[i3] = point.x;
    positions[i3 + 1] = point.y;
    positions[i3 + 2] = point.z;
    
    // Set colors with variation
    const baseColor = new THREE.Color(color);
    const r = baseColor.r + (Math.random() * 2 - 1) * colorVariation;
    const g = baseColor.g + (Math.random() * 2 - 1) * colorVariation;
    const b = baseColor.b + (Math.random() * 2 - 1) * colorVariation;
    
    colors[i3] = Math.max(0, Math.min(1, r));
    colors[i3 + 1] = Math.max(0, Math.min(1, g));
    colors[i3 + 2] = Math.max(0, Math.min(1, b));
  });
  
  // Set attributes
  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
  
  // Create material
  const material = new THREE.PointsMaterial({
    size,
    vertexColors: true,
    sizeAttenuation: true
  });
  
  // Create points
  return new THREE.Points(geometry, material);
};

/**
 * Create a raycaster for point interaction
 * @param {THREE.Camera} camera - Camera to use for raycasting
 * @returns {THREE.Raycaster} Raycaster object
 */
export const createRaycaster = (camera) => {
  const raycaster = new THREE.Raycaster();
  raycaster.params.Points.threshold = 0.1; // Adjust threshold for better point detection
  
  return raycaster;
};

/**
 * Get mouse position in normalized device coordinates
 * @param {Event} event - Mouse event
 * @returns {THREE.Vector2} Normalized mouse position
 */
export const getNormalizedMousePosition = (event) => {
  const mouse = new THREE.Vector2();
  
  // Calculate normalized device coordinates (-1 to +1)
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  
  return mouse;
};

/**
 * Find intersected points
 * @param {THREE.Raycaster} raycaster - Raycaster to use
 * @param {THREE.Vector2} mouse - Normalized mouse position
 * @param {Array} objects - Array of objects to check for intersections
 * @returns {Array} Array of intersected objects
 */
export const findIntersectedPoints = (raycaster, mouse, objects) => {
  raycaster.setFromCamera(mouse, camera);
  return raycaster.intersectObjects(objects);
};

// Export all functions as a default object
export default {
  createScene,
  createCamera,
  createRenderer,
  handleResize,
  createPointCloud,
  createRaycaster,
  getNormalizedMousePosition,
  findIntersectedPoints
};
