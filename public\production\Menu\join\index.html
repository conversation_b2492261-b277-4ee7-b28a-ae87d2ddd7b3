<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply to Join A Community - Homara</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="stylesheet" href="../../sidebar.css">
    <link rel="icon" href="../../homara1.png">
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button id="toggle-sidebar">MENU</button>

    <!-- Sidebar -->
    <div id="sidebar" class="hidden">
        <div class="sidebar-header">
            <h2 id="sidebar-title">Community</h2>
            <button id="close-sidebar">×</button>
        </div>
        <div class="sidebar-content">
            <!-- Top half: Navigation links -->
            <div class="sidebar-nav">
                <div class="nav-item" data-page="join">
                    <h3>APPLY TO JOIN A COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="build">
                    <h3>BUILD YOUR OWN COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="news">
                    <h3>NEWS</h3>
                </div>
                <div class="nav-item" data-page="learn">
                    <h3>INFO</h3>
                </div>
                <div class="nav-item" data-page="home">
                    <h3>BACK TO HOMARA</h3>
                </div>
            </div>

            <!-- Divider -->
            <div class="sidebar-divider"></div>

            <!-- Bottom half: Viewed points history -->
            <div class="sidebar-history">
                <h3>RECENTLY VIEWED POINTS</h3>
                <div id="points-history" class="points-history-container">
                    <!-- Points history will be populated dynamically -->
                    <div class="empty-history-message">
                        Your viewed points will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <header>
            <h1>Apply to Join A Community</h1>
        </header>

        <section>
            <h2>Connect with Existing Communities</h2>
            <p>
                Homara hosts a variety of communities organized by both location and interest.
                Apply to join an existing community to connect with like-minded individuals.
            </p>
        </section>

        <section>
            <h2>Location-Based Communities</h2>
            <p>
                Our location-based communities bring together people from the same geographical area.
                These communities are accessible at Homara.com/LocationName.
            </p>
            <div class="grid">
                <div class="grid-item">
                    <h3>New York</h3>
                    <p>Connect with others in the New York area</p>
                    <a href="/NewYork" class="button">Visit</a>
                </div>
                <div class="grid-item">
                    <h3>San Francisco</h3>
                    <p>Join the San Francisco community</p>
                    <a href="/SanFrancisco" class="button">Visit</a>
                </div>
                <div class="grid-item">
                    <h3>Chicago</h3>
                    <p>Connect with the Chicago community</p>
                    <a href="/Chicago" class="button">Visit</a>
                </div>
                <div class="grid-item">
                    <h3>Los Angeles</h3>
                    <p>Join the Los Angeles community</p>
                    <a href="/LosAngeles" class="button">Visit</a>
                </div>
            </div>
        </section>

        <section>
            <h2>Application Form</h2>
            <p>
                To join a private community, please fill out the application form below.
                The community owner will review your application and contact you.
            </p>
            <form>
                <div class="form-group">
                    <label for="name">Your Name</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="community">Community You Want to Join</label>
                    <input type="text" id="community" required>
                </div>
                <div class="form-group">
                    <label for="reason">Why Do You Want to Join?</label>
                    <textarea id="reason" rows="4" required></textarea>
                </div>
                <button type="submit">Submit Application</button>
            </form>
        </section>
    </div>

    <!-- Add sidebar functionality -->
    <script src="../../src/HomaraStart/Sidebar.js"></script>
    <script>
        // Initialize sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = new Sidebar();

            // Set up event listeners for navigation items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    switch(page) {
                        case 'join':
                            window.location.href = '../join/index.html';
                            break;
                        case 'build':
                            window.location.href = '../build/index.html';
                            break;
                        case 'news':
                            window.location.href = '../news/index.html';
                            break;
                        case 'learn':
                            window.location.href = '../learn/index.html';
                            break;
                        case 'home':
                            window.location.href = '../../index.html';
                            break;
                    }
                });
            });
        });
    </script>
</body>
</html>
