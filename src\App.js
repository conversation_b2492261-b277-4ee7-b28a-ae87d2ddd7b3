import React, { useEffect } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import TestConnection from './components/TestConnection';
import { ROUTES } from './components/utils/constants';
import testConnection from './utils/testBackendConnection';

// Import your other components here
// import HomePage from './components/pages/HomePage';
// import UserCommunityPage from './components/pages/UserCommunityPage';
// etc.

function App() {
  // Test backend connection when the app loads
  useEffect(() => {
    const runConnectionTest = async () => {
      await testConnection();
    };

    runConnectionTest();
  }, []);

  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.SIGNUP} element={<Register />} />
          <Route path="/test-connection" element={<TestConnection />} />

          {/* Add your other routes here */}
          {/* <Route path={ROUTES.HOME} element={<HomePage />} /> */}
          {/* <Route path={ROUTES.USER_COMMUNITY} element={<UserCommunityPage />} /> */}

          {/* Default route */}
          <Route path="*" element={<div>Page not found</div>} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
