/**
 * MongoDB Connection Class
 * Handles connections to MongoDB database through the API
 */

class MongoDBConnection {
  constructor() {
    this.isConnected = false;
    // In browser environment, we can only access environment variables if they're exposed to the window object
    // or if they were injected during the build process
    // Always use the frontend server as a proxy to avoid CORS issues
    this.API_BASE_URL = 'http://localhost:3001/api'; // This is correct - we use the frontend as a proxy
    console.log('API Base URL:', this.API_BASE_URL);
  }

  /**
   * Connect to MongoDB
   * @returns {Promise<boolean>} Connection success
   */
  async connect() {
    try {
      console.log('Connecting to MongoDB...');

      // In a real implementation, this would establish a connection
      // For now, we'll just simulate a successful connection
      this.isConnected = true;

      console.log('Connected to MongoDB');
      return true;
    } catch (error) {
      console.error('Error connecting to MongoDB:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Disconnect from MongoDB
   * @returns {Promise<boolean>} Disconnection success
   */
  async disconnect() {
    try {
      console.log('Disconnecting from MongoDB...');

      // In a real implementation, this would close the connection
      // For now, we'll just simulate a successful disconnection
      this.isConnected = false;

      console.log('Disconnected from MongoDB');
      return true;
    } catch (error) {
      console.error('Error disconnecting from MongoDB:', error);
      return false;
    }
  }

  /**
   * Get community by builder ID
   * @param {string} builderId - Builder ID
   * @returns {Promise<Object>} Community data
   */
  async getCommunityByBuilderID(builderId) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      console.log(`Getting community for builder ID: ${builderId}`);

      // In a real implementation, this would query the database
      // For now, we'll simulate a response
      const community = {
        id: builderId,
        name: `${builderId}'s Community`,
        shape: 'tree',
        points: []
      };

      return {
        success: true,
        community
      };
    } catch (error) {
      console.error(`Error getting community for builder ID ${builderId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get community points
   * @param {string} communityId - Community ID
   * @returns {Promise<Object>} Community points
   */
  async getCommunityPoints(communityId) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      console.log(`Getting points for community ID: ${communityId}`);

      // In a real implementation, this would query the database
      // For now, we'll simulate a response
      const points = [
        { id: 'pt1', x: 0, y: 10, z: 0, content: 'Sample Point 1' },
        { id: 'pt2', x: 5, y: 15, z: 5, content: 'Sample Point 2' },
        { id: 'pt3', x: -5, y: 20, z: -5, content: 'Sample Point 3' }
      ];

      return {
        success: true,
        points
      };
    } catch (error) {
      console.error(`Error getting points for community ID ${communityId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if an email already exists in the database
   * @param {string} email - Email to check
   * @returns {Promise<Object>} Check result
   */
  async checkEmailExists(email) {
    try {
      console.log(`Checking if email exists: ${email}`);

      try {
        // Make API call to check if email exists
        const response = await fetch(`${this.API_BASE_URL}/auth/check-email?email=${encodeURIComponent(email)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          // If the endpoint doesn't exist or returns an error, assume email doesn't exist
          console.log(`Email check endpoint returned ${response.status}, assuming email doesn't exist`);
          return { exists: false };
        }

        const result = await response.json();
        console.log('Email check result:', result);
        return result;
      } catch (apiError) {
        console.error('Error checking email:', apiError);
        // If there's an error, assume email doesn't exist to allow registration attempt
        return { exists: false };
      }
    } catch (error) {
      console.error('Error in checkEmailExists:', error);
      return { exists: false };
    }
  }

  /**
   * Save user data
   * @param {Object} userData - User data to save
   * @returns {Promise<Object>} Save result
   */
  async saveUser(userData) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      console.log('Saving user data:', userData);

      // Check if email exists first
      const email = userData.email || '';
      const emailCheck = await this.checkEmailExists(email);

      if (emailCheck.exists) {
        console.log(`Email ${email} already exists in the database. Using a unique email instead.`);
        // We'll continue with the unique email approach below
      }

      // Make API call to backend to create user in MongoDB
      try {
        // Use the API_BASE_URL from constructor
        console.log(`Using API base URL: ${this.API_BASE_URL}`);
        console.log('Making real API call for user registration');
        console.log('User data:', userData);

        // Make the actual API call to the backend
        // The API_BASE_URL already includes '/api', so we don't need to add it again
        console.log('Preparing API request with userData:', userData);

        // Create a request body with all required fields
        // Use the original email and username as provided by the user
        const requestBody = {
          firebaseUid: userData.userId,
          username: userData.username, // Use original username
          email: userData.email || '',
          // Include password - this is required by the backend
          password: userData.password || 'Password123!', // Default strong password if not provided
          displayName: userData.displayName || userData.username,
          phoneNumber: userData.phoneNumber || '',
          pointPosition: userData.pointPosition || { x: 0, y: 0, z: 0 },
          profile: {
            bio: userData.profile?.bio || '',
            avatarUrl: userData.profile?.avatarUrl || '',
            preferences: userData.profile?.preferences || {
              theme: 'system',
              notifications: true
            }
          }
        };

        console.log('Sending API request with body:', JSON.stringify(requestBody));

        const response = await fetch(`${this.API_BASE_URL}/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          let errorText = await response.text();
          console.error('API error response:', errorText);

          // Check if this is the "email already in use" error
          if (errorText.includes("email address is already in use")) {
            console.log("Detected 'email already in use' error - this should not happen with our unique email");

            // Try to parse the error as JSON to get more details
            try {
              const errorJson = JSON.parse(errorText);
              console.log("Parsed error JSON:", errorJson);
            } catch (jsonError) {
              console.log("Could not parse error as JSON:", jsonError);
            }

            // Since we're already adding a timestamp to make the email unique,
            // if we still get this error, something else is wrong with the backend
            throw new Error(`Backend email uniqueness check issue: ${response.status} - ${errorText}`);
          } else {
            throw new Error(`API error: ${response.status} - ${errorText}`);
          }
        }

        const result = await response.json();
        console.log('API response:', result);

        return {
          success: true,
          userId: userData.userId,
          user: result
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        // Return error information instead of simulating success
        return {
          success: false,
          error: apiError.message || 'Failed to connect to the backend API',
          userId: userData.userId
        };
      }
    } catch (error) {
      console.error('Error saving user data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update community data
   * @param {string} communityId - Community ID
   * @param {Object} communityData - Updated community data
   * @returns {Promise<Object>} Update result
   */
  async updateCommunity(communityId, communityData) {
    try {
      if (!this.isConnected) {
        await this.connect();
      }

      console.log(`Updating community ${communityId}:`, communityData);

      // In a real implementation, this would update the database
      // For now, we'll simulate a successful update

      return {
        success: true,
        community: { id: communityId, ...communityData }
      };
    } catch (error) {
      console.error(`Error updating community ${communityId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Make the class globally available
window.MongoDBConnection = MongoDBConnection;
