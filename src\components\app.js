/**
 * Main App Component
 * Entry point for the Homara website application
 */

import routing from './routes';
import state from './state';
import api from './services/api';
import urlParser from './utils/urlParser';
import { STORAGE_KEYS } from './utils/constants';

// Main App class
class App {
  constructor() {
    // Initialize state
    this.state = state;
    this.state.initializeState();

    // Initialize API
    this.api = api;

    // Check if animation has been played before
    this.animationPlayed = localStorage.getItem(STORAGE_KEYS.ANIMATION_PLAYED) === 'true';

    // Initialize Firebase Auth
    this.firebaseAuth = null;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      console.log('Initializing Homara application');

      // Initialize Firebase Auth
      this.initializeFirebase();

      // Initialize API with Firebase Auth
      this.api.initialize(this.firebaseAuth);

      // Initialize routing
      routing.initializeRouting();

      // Check if we need to load a user community
      this.checkForUserCommunity();

      // Add event listeners
      this.addEventListeners();

      console.log('Homara application initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing Homara application:', error);
      return false;
    }
  }

  /**
   * Initialize Firebase
   */
  initializeFirebase() {
    try {
      // In a real implementation, this would initialize Firebase
      // For now, we'll create a new instance of FirebaseAuth
      this.firebaseAuth = new FirebaseAuth();
      this.firebaseAuth.initialize();

      console.log('Firebase initialized');
    } catch (error) {
      console.error('Error initializing Firebase:', error);
    }
  }

  /**
   * Check if the current URL is for a user's community
   */
  async checkForUserCommunity() {
    try {
      const username = urlParser.extractUsernameFromURL();

      if (username) {
        console.log(`Loading community for user: ${username}`);

        // Update UI state to show loading
        this.state.updateUIState({ isLoading: true });

        // Load the user's community
        const communityService = await import('./services/communityService').then(m => m.default);
        const community = await communityService.getCommunityByUsername(username);

        // Update community state
        this.state.updateCommunityState({
          currentCommunity: community,
          communityId: community.id,
          builderId: community.builderId,
          communityShape: community.communityShape
        });

        // Load community points
        const points = await communityService.getCommunityPoints(community.id);
        this.state.updateCommunityState({ points });

        // Update UI state
        this.state.updateUIState({
          isLoading: false,
          sidebarOpen: true
        });
      }
    } catch (error) {
      console.error('Error loading user community:', error);
      this.state.updateUIState({
        isLoading: false,
        error: 'Failed to load community'
      });
    }
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    // Listen for authentication state changes
    if (this.firebaseAuth) {
      this.firebaseAuth.addEventListener('auth-state-changed', (user) => {
        if (user) {
          this.state.updateUserState({
            isLoggedIn: true,
            username: user.username,
            userId: user.uid,
            token: 'simulated-token'
          });
        } else {
          this.state.clearUserState();
        }
      });
    }

    // Listen for window resize events
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // This would update the 3D environment if needed
    console.log('Window resized');
  }

  /**
   * Start the application
   */
  start() {
    console.log('Starting Homara application');

    // Check if we should play the intro animation
    if (!this.animationPlayed) {
      // Play intro animation
      this.playIntroAnimation();
    } else {
      // Skip animation and render the current route
      routing.renderCurrentRoute();
    }
  }

  /**
   * Play intro animation
   */
  playIntroAnimation() {
    console.log('Playing intro animation');

    // In a real implementation, this would play the animation
    // For now, we'll just set a timeout to simulate the animation
    setTimeout(() => {
      // Mark animation as played
      localStorage.setItem(STORAGE_KEYS.ANIMATION_PLAYED, 'true');
      this.state.updateUIState({ animationPlayed: true });

      // Render the current route
      routing.renderCurrentRoute();
    }, 100); // In a real implementation, this would be the animation duration
  }
}

// Export the App class
export default App;