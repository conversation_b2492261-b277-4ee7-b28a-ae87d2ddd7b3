/**
 * Routes for the Homara Website
 * Defines all application routes and their handlers
 */

import { ROUTES } from './utils/constants';
import urlParser from './utils/urlParser';

// Import page components (these would be actual imports in a real implementation)
// For now, we'll define placeholder components
const HomePage = { render: () => console.log('Rendering Home Page') };
const BuildPage = { render: () => console.log('Rendering Build Page') };
const JoinPage = { render: () => console.log('Rendering Join Page') };
const LearnPage = { render: () => console.log('Rendering Learn Page') };
const NewsPage = { render: () => console.log('Rendering News Page') };
const LoginPage = { render: () => console.log('Rendering Login Page') };
const SignupPage = { render: () => console.log('Rendering Signup Page') };
const AboutPage = { render: () => console.log('Rendering About Page') };
const UserCommunityPage = { render: (username) => console.log(`Rendering User Community Page for ${username}`) };
const NotFoundPage = { render: () => console.log('Rendering 404 Page') };

// Define routes
const routes = [
  {
    path: ROUTES.HOME,
    component: HomePage,
    exact: true
  },
  {
    path: ROUTES.BUILD,
    component: BuildPage
  },
  {
    path: ROUTES.JOIN,
    component: JoinPage
  },
  {
    path: ROUTES.LEARN,
    component: LearnPage
  },
  {
    path: ROUTES.NEWS,
    component: NewsPage
  },
  {
    path: ROUTES.LOGIN,
    component: LoginPage
  },
  {
    path: ROUTES.SIGNUP,
    component: SignupPage
  },
  {
    path: ROUTES.ABOUT,
    component: AboutPage
  },
  {
    path: ROUTES.USER_COMMUNITY,
    component: UserCommunityPage,
    // This is a dynamic route that matches any username
    // We'll use a custom matcher to ensure it doesn't match known routes
    matcher: () => {
      const username = urlParser.extractUsernameFromURL();
      return username ? { username } : null;
    }
  }
];

/**
 * Find the matching route for the current path
 * @returns {Object} Matching route and params
 */
export const findMatchingRoute = () => {
  const path = window.location.pathname;

  // Check for exact matches first
  const exactMatch = routes.find(route =>
    route.exact && route.path === path
  );

  if (exactMatch) {
    return { route: exactMatch, params: {} };
  }

  // Check for regular matches
  for (const route of routes) {
    // Skip exact routes as we've already checked them
    if (route.exact) continue;

    // If the route has a custom matcher, use it
    if (route.matcher) {
      const params = route.matcher(path);
      if (params) {
        return { route, params };
      }
    }
    // Otherwise, check if the path starts with the route path
    else if (path.startsWith(route.path)) {
      return { route, params: {} };
    }
  }

  // If no match is found, return the 404 route
  return {
    route: {
      path: '/404',
      component: NotFoundPage
    },
    params: {}
  };
};

/**
 * Navigate to a route
 * @param {string} path - Path to navigate to
 */
export const navigateTo = (path) => {
  window.history.pushState({}, '', path);
  renderCurrentRoute();
};

/**
 * Render the current route
 */
export const renderCurrentRoute = () => {
  const { route, params } = findMatchingRoute();

  // Render the component for the matching route
  if (route.component && route.component.render) {
    route.component.render(params);
  }
};

/**
 * Initialize routing
 */
export const initializeRouting = () => {
  // Handle initial route
  renderCurrentRoute();

  // Handle browser back/forward navigation
  window.addEventListener('popstate', () => {
    renderCurrentRoute();
  });

  // Intercept link clicks for client-side routing
  document.addEventListener('click', (event) => {
    // Check if the clicked element is a link
    let element = event.target;
    while (element && element.tagName !== 'A') {
      element = element.parentElement;
    }

    // If it's not a link or it's an external link, let the browser handle it
    if (!element || element.target === '_blank' || element.href.startsWith('http')) {
      return;
    }

    // Prevent default link behavior
    event.preventDefault();

    // Navigate to the link's href
    navigateTo(element.pathname);
  });
};

// Export all routing functions
export default {
  routes,
  findMatchingRoute,
  navigateTo,
  renderCurrentRoute,
  initializeRouting
};