/**
 * Point Menu System
 * Orchestrates the point animation and clean menu display
 */

class PointMenu {
    constructor() {
        this.animation = null;
        this.cleanMenu = null;
        this.animationDelay = 8000; // 8 seconds - wait for intro animation to fully complete
    }

    // Method to be called when intro animation completes
    triggerAfterIntroComplete() {
        console.log('PointMenu: Intro animation complete, waiting before starting menu...');
        setTimeout(() => {
            console.log('PointMenu: Starting point menu animation now...');
            this.startPointAnimation();
        }, this.animationDelay);
    }

    // Legacy method - keeping for compatibility but with longer delay
    triggerAfterEnvironmentLoad() {
        console.log('PointMenu: Environment loaded, waiting longer before starting menu...');
        setTimeout(() => {
            console.log('PointMenu: Starting point menu animation now...');
            this.startPointAnimation();
        }, this.animationDelay);
    }

    startPointAnimation() {
        console.log('Starting point menu system...');

        // Create the animation with completion callback
        this.animation = new PointMenuAnimation(() => {
            this.onAnimationComplete();
        });

        // Start the animation
        this.animation.startAnimation();
    }

    onAnimationComplete() {
        console.log('Point animation complete, showing clean menu...');

        // Create and show the clean menu
        this.cleanMenu = new CleanMenu();
        this.cleanMenu.show();
    }

    // Clean up method
    destroy() {
        if (this.animation) {
            this.animation.destroy();
        }
        if (this.cleanMenu) {
            this.cleanMenu.destroy();
        }
    }
}

// Make globally available
window.PointMenu = PointMenu;
