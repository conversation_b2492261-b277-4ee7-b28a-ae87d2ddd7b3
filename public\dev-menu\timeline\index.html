<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Timeline - Development</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="icon" href="../../images/homara1.png">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #00ff00, #00cc00);
            transform: translateX(-50%);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 50px;
            width: 50%;
        }
        
        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 40px;
            text-align: right;
        }
        
        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 40px;
            text-align: left;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 12px;
            height: 12px;
            background: #00ff00;
            border-radius: 50%;
            z-index: 1;
        }
        
        .timeline-item:nth-child(odd)::before {
            right: -6px;
        }
        
        .timeline-item:nth-child(even)::before {
            left: -6px;
        }
        
        .timeline-content {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .timeline-date {
            color: #00ff00;
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .timeline-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: #ffffff;
        }
        
        .timeline-description {
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .status-completed {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
            border: 1px solid #00ff00;
        }
        
        .status-current {
            background: rgba(255, 255, 0, 0.2);
            color: #ffff00;
            border: 1px solid #ffff00;
        }
        
        .status-planned {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        @media (max-width: 768px) {
            .timeline::before {
                left: 20px;
            }
            
            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 50px !important;
                padding-right: 0 !important;
                text-align: left !important;
            }
            
            .timeline-item::before {
                left: 14px !important;
            }
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← Back to Homara</a>
    
    <div class="container">
        <div class="header">
            <h1>Our Timeline</h1>
            <p>The journey of building Homara - where we've been and where we're going</p>
        </div>
        
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q2 2024</div>
                    <div class="timeline-title">Concept & Vision</div>
                    <div class="timeline-description">
                        Initial concept development for 3D community visualization. Research into spatial interfaces and community dynamics.
                    </div>
                    <span class="status-badge status-completed">Completed</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q3 2024</div>
                    <div class="timeline-title">Technical Foundation</div>
                    <div class="timeline-description">
                        Built core 3D rendering system using Three.js. Developed initial point cloud visualization and camera controls.
                    </div>
                    <span class="status-badge status-completed">Completed</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q4 2024</div>
                    <div class="timeline-title">Authentication & Backend</div>
                    <div class="timeline-description">
                        Implemented Firebase authentication system and MongoDB integration. Created user management and community data structures.
                    </div>
                    <span class="status-badge status-completed">Completed</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q1 2025</div>
                    <div class="timeline-title">UI/UX Development</div>
                    <div class="timeline-description">
                        Focus on user interface design and experience. Developing new menu systems, animations, and interaction patterns.
                    </div>
                    <span class="status-badge status-current">Current Phase</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q2 2025</div>
                    <div class="timeline-title">Community Features</div>
                    <div class="timeline-description">
                        Implement core community features: member interactions, content sharing, and community customization tools.
                    </div>
                    <span class="status-badge status-planned">Planned</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q3 2025</div>
                    <div class="timeline-title">Beta Testing</div>
                    <div class="timeline-description">
                        Launch closed beta with select communities. Gather feedback and iterate on core features and performance.
                    </div>
                    <span class="status-badge status-planned">Planned</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q4 2025</div>
                    <div class="timeline-title">Multi-Community Support</div>
                    <div class="timeline-description">
                        Expand to support multiple communities with different shapes, themes, and customization options.
                    </div>
                    <span class="status-badge status-planned">Planned</span>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date">Q1 2026</div>
                    <div class="timeline-title">Public Launch</div>
                    <div class="timeline-description">
                        Official public launch with community creation tools, desktop application, and full feature set.
                    </div>
                    <span class="status-badge status-planned">Planned</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
