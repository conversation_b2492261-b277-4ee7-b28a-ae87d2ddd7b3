<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News - Homara</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="stylesheet" href="../../sidebar.css">
    <link rel="icon" href="../../homara1.png">
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button id="toggle-sidebar">MENU</button>

    <!-- Sidebar -->
    <div id="sidebar" class="hidden">
        <div class="sidebar-header">
            <h2 id="sidebar-title">Community</h2>
            <button id="close-sidebar">×</button>
        </div>
        <div class="sidebar-content">
            <!-- Top half: Navigation links -->
            <div class="sidebar-nav">
                <div class="nav-item" data-page="join">
                    <h3>APPLY TO JOIN A COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="build">
                    <h3>BUILD YOUR OWN COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="news">
                    <h3>NEWS</h3>
                </div>
                <div class="nav-item" data-page="learn">
                    <h3>INFO</h3>
                </div>
                <div class="nav-item" data-page="home">
                    <h3>BACK TO HOMARA</h3>
                </div>
            </div>

            <!-- Divider -->
            <div class="sidebar-divider"></div>

            <!-- Bottom half: Viewed points history -->
            <div class="sidebar-history">
                <h3>RECENTLY VIEWED POINTS</h3>
                <div id="points-history" class="points-history-container">
                    <!-- Points history will be populated dynamically -->
                    <div class="empty-history-message">
                        Your viewed points will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <header>
            <h1>Homara News</h1>
        </header>

        <section>
            <h2>Latest Updates</h2>
            <p>
                Stay informed about the latest developments, features, and community news from Homara.
            </p>
        </section>

        <section class="news-item">
            <h2>Homara Platform Launch</h2>
            <p class="date">April 15, 2023</p>
            <p>
                We're excited to announce the official launch of the Homara platform! After months of development,
                we're proud to introduce a new way for people to connect and share information in a visually
                engaging and spatially organized environment.
            </p>
            <p>
                The initial release includes the core features of our platform:
            </p>
            <ul>
                <li>3D community environments with interactive points</li>
                <li>Desktop application for community customization</li>
                <li>User-friendly navigation and search functionality</li>
                <li>Location-based communities for geographical connections</li>
            </ul>
            <p>
                We're starting with our flagship tree-shaped community template, with more shapes coming soon.
            </p>
        </section>

        <section class="news-item">
            <h2>New York Community Now Live</h2>
            <p class="date">April 10, 2023</p>
            <p>
                We're thrilled to announce that our first location-based community, New York, is now live!
                Visit <a href="/NewYork">Homara.com/NewYork</a> to connect with others in the New York area
                and explore the community's shared content.
            </p>
            <p>
                This is the first of many location-based communities we plan to launch in the coming months.
                Stay tuned for announcements about communities in other cities!
            </p>
        </section>

        <section class="news-item">
            <h2>Desktop Application Beta Release</h2>
            <p class="date">April 5, 2023</p>
            <p>
                The beta version of our desktop application is now available for download! This application
                allows community owners to customize their community, add new points, and update content.
            </p>
            <p>
                Key features of the beta release:
            </p>
            <ul>
                <li>Community customization tools</li>
                <li>Point creation and management</li>
                <li>Content editor with support for text, images, and links</li>
                <li>Synchronization with our cloud database</li>
            </ul>
            <p>
                <a href="../build/index.html" class="button">Download Beta</a>
            </p>
        </section>

        <section class="news-item">
            <h2>Coming Soon: New Community Shapes</h2>
            <p class="date">April 1, 2023</p>
            <p>
                We're working on expanding our library of community shapes beyond the initial tree template.
                In the coming months, we'll be introducing new shapes that offer different ways to organize
                and visualize your community's content.
            </p>
            <p>
                Stay tuned for announcements about new shape releases!
            </p>
        </section>
    </div>

    <!-- Add sidebar functionality -->
    <script src="../../src/HomaraStart/Sidebar.js"></script>
    <script>
        // Initialize sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = new Sidebar();

            // Set up event listeners for navigation items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    switch(page) {
                        case 'join':
                            window.location.href = '../join/index.html';
                            break;
                        case 'build':
                            window.location.href = '../build/index.html';
                            break;
                        case 'news':
                            window.location.href = '../news/index.html';
                            break;
                        case 'learn':
                            window.location.href = '../learn/index.html';
                            break;
                        case 'home':
                            window.location.href = '../../index.html';
                            break;
                    }
                });
            });
        });
    </script>
</body>
</html>
