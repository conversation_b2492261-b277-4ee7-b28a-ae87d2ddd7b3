console.log('Main.js file loaded');

// Use global THREE object
// THREE will be loaded from script tags in index.html

// Our modules will be loaded via script tags

// THREE is now loaded globally via script tags in index.html

// Initialize the application
function initApp() {
    console.log('Initializing app with script tags...');
    console.log('THREE available:', typeof THREE !== 'undefined');

    // Create scene but don't display it yet
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000000); // Pure black background

    // Camera and renderer setup
    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000); // Reduced FOV for better view

    // Set camera position to be at a good height and distance to see the tree
    const INITIAL_CAMERA_POSITION = new THREE.Vector3(0, 60, 40); // Adjusted height, closer distance
    camera.position.copy(INITIAL_CAMERA_POSITION);
    camera.lookAt(0, 45, 0); // Look at an even higher point on the tree

    const renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance'
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio); // For sharper rendering
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Create a container for the 3D scene that starts invisible
    const sceneContainer = document.createElement('div');
    sceneContainer.id = 'scene-container';
    sceneContainer.style.opacity = '0';
    sceneContainer.style.transition = 'opacity 1s ease-in';
    sceneContainer.appendChild(renderer.domElement);
    document.body.appendChild(sceneContainer);

    // Initialize camera controller with initial position
    const cameraController = new CameraController(camera, renderer.domElement, INITIAL_CAMERA_POSITION);

    // Add zoom limits appropriate for the view
    cameraController.setZoomLimits(5, 100); // Min distance 5, Max distance 100

    // Prevent spacebar from resetting camera
    window.addEventListener('keydown', function(e) {
        // If the key pressed is spacebar (keyCode 32)
        if (e.code === 'Space') {
            // Prevent the default action
            e.preventDefault();
        }
    });

    // Add environment (ground and lighting)
    const environment = new Environment();
    scene.add(environment.group);

    // Create environment loader
    const envLoader = new EnvLoader();

    // Variable to store current community data
    let currentCommunity = null;

    // Function to get builder ID from URL parameters
    const getBuilderIDFromURL = () => {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('builder') || 'homara1'; // Default to homara1 if not specified
    };

    // Function to load environment by builder ID
    const loadEnvironment = async (builderID) => {
        try {
            console.log(`Loading environment for builder: ${builderID}`);

            // Load environment with builder ID
            await envLoader.loadEnvironment(builderID);

            // Add environment to scene
            scene.add(envLoader.group);

            // Store current community data
            currentCommunity = {
                builderID: builderID,
                shape: envLoader.communityShape,
                points: envLoader.communityPoints
            };

            // Update sidebar with community info
            if (sidebar) {
                sidebar.updateContent(`${builderID}'s Community`);

                // Connect tree to sidebar for point history
                if (envLoader.shapeInstance) {
                    envLoader.shapeInstance.setSidebar(sidebar);
                }
            }

            console.log('Environment loaded successfully');

            // Point menu is now triggered from intro animation completion, not environment loading
        } catch (error) {
            console.error('Error loading environment:', error);
        }
    };

    // Function to search for communities
    const searchCommunities = async (query) => {
        // This would be replaced with actual database query
        console.log(`Searching for communities matching: ${query}`);

        // Simulate search results
        const results = await simulateSearchQuery(query);

        if (results.length > 0) {
            // Load the first result
            loadEnvironment(results[0].builderID);

            // Display search results (this would be a dropdown in the UI)
            console.log('Search results:', results);
        } else {
            console.log('No communities found matching the query');
        }
    };

    // Simulate database search (would be replaced with actual database query)
    const simulateSearchQuery = async (query) => {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 300));

        // Mock database of communities
        const communities = [
            { builderID: 'homara1', location: 'New York', shape: 'tree' },
            { builderID: 'community2', location: 'Los Angeles', shape: 'tree' },
            { builderID: 'community3', location: 'Chicago', shape: 'tree' },
            { builderID: 'community4', location: 'New York', shape: 'tree' }
        ];

        // Search by builder ID or location
        return communities.filter(community =>
            community.builderID.toLowerCase().includes(query.toLowerCase()) ||
            community.location.toLowerCase().includes(query.toLowerCase())
        );
    };

    // Start loading the default environment (homara1)
    const defaultBuilderID = getBuilderIDFromURL();
    loadEnvironment(defaultBuilderID);

    // Create UI components but keep them hidden initially
    const controlsOverlay = new ControlsOverlay();
    const sidebar = new Sidebar();

    // Initialize search with dropdown functionality
    const search = new Search();

    // Initialize the new point menu system
    const pointMenu = new PointMenu();

    // Hide the old sidebar toggle button - using new point menu instead
    // const sidebarToggle = document.createElement('button');
    // sidebarToggle.id = 'toggle-sidebar';
    // sidebarToggle.textContent = 'MENU'; // Vertical text will be handled by CSS
    // sidebarToggle.addEventListener('click', () => sidebar.toggle());
    // document.body.appendChild(sidebarToggle);

    // For testing purposes, always play the animation
    const isFirstVisit = true;
    // In production, use this instead:
    // const isFirstVisit = !localStorage.getItem('hasVisitedHomara');

    if (isFirstVisit) {
        // Initialize loading animation for first-time visitors
        const loadingAnimation = new LoadingAnimation();

        // Set callback to fade in the scene when loading completes
        loadingAnimation.setOnComplete(() => {
            // Fade in the scene
            sceneContainer.style.opacity = '1';

            // Show UI elements after the scene fades in
            setTimeout(() => {
                // Show search bar
                search.show();

                // Don't show sidebar - using new point menu instead
                // sidebar.show();

                // Show controls overlay
                controlsOverlay.show(8000); // Show for 8 seconds

                // Trigger point menu after intro animation completes
                console.log('Intro animation complete, triggering point menu...');
                pointMenu.triggerAfterIntroComplete();

                // Set flag in localStorage to indicate the user has visited before
                localStorage.setItem('hasVisitedHomara', 'true');
            }, 500);
        });

        // Start the animation
        loadingAnimation.init();
    } else {
        // For returning visitors, skip the animation
        console.log('Returning visitor - skipping intro animation');

        // Immediately show the scene and UI
        sceneContainer.style.opacity = '1';
        search.show();
        // Don't show sidebar - using new point menu instead
        // sidebar.show();
        controlsOverlay.show(8000); // Show for 8 seconds

        // Trigger point menu for returning visitors too (but with shorter delay)
        console.log('Returning visitor - triggering point menu...');
        setTimeout(() => {
            pointMenu.triggerAfterIntroComplete();
        }, 2000); // Shorter delay for returning visitors
    }

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);

        // Update environment if it's loaded
        if (envLoader && envLoader.shapeInstance) {
            envLoader.update(camera);
        }

        cameraController.update();
        renderer.render(scene, camera);
    }

    animate();

    // Handle window resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
}

// Start the initialization process immediately
initApp();
