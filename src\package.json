{"name": "homara-website", "version": "1.0.0", "description": "Homara Website - 3D Community Visualization", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"firebase": "^9.22.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "three": "^0.152.2"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}