/* Global Styles */
@import url('https://fonts.cdnfonts.com/css/neue-haas-grotesk-display-pro');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Neue Haas Grotesk Display Pro', 'Arial', sans-serif;
}

body {
    overflow: hidden;
    background-color: #000;
}

.hidden {
    display: none !important;
}

/* Animation Styles for StartAnimate.js */
.start-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.start-animation.visible {
    opacity: 1;
}

.start-animation.fade-out {
    opacity: 0;
}

.logo {
    font-size: 4rem;
    color: #fff;
    letter-spacing: 10px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
    animation: pulse 2s infinite;
}

.subtitle {
    color: #ccc;
    font-size: 1.2rem;
    margin-top: 10px;
    text-align: center;
}

@keyframes pulse {
    0% { opacity: 0.7; text-shadow: 0 0 10px rgba(255, 255, 255, 0.7); }
    50% { opacity: 1; text-shadow: 0 0 20px rgba(255, 255, 255, 1); }
    100% { opacity: 0.7; text-shadow: 0 0 10px rgba(255, 255, 255, 0.7); }
}

.particles-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Search Container */
#search-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 100;
    transition: opacity 0.5s ease;
}

.search-wrapper {
    position: relative;
    width: 300px;
}

#search-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 0;
    outline: none;
    font-weight: 400;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

#search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

#search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-top: none;
    z-index: 101;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.search-result {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.search-result-name {
    font-weight: 500;
    color: white;
    margin-bottom: 3px;
}

.search-result-type {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Sidebar */
#sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    z-index: 100;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.5);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
}

#sidebar.visible {
    transform: translateX(0);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: rgba(0, 0, 0, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-header h2 {
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

#close-sidebar {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.sidebar-content {
    padding: 20px;
    overflow-y: auto;
    height: calc(100% - 60px);
}

/* Navigation section */
.sidebar-nav {
    margin-bottom: 30px;
}

.nav-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateX(5px);
}

.nav-item h3 {
    font-size: 0.8rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin: 0;
}

/* Divider */
.sidebar-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 30px 0;
}

/* Points history section */
.sidebar-history {
    margin-bottom: 30px;
}

.sidebar-history h3 {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

.points-history-container {
    max-height: 300px;
    overflow-y: auto;
}

.empty-history-message {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
    text-align: center;
    padding: 20px 0;
    font-style: italic;
}

/* Point history item (for future use) */
.point-history-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.3);
}

.point-preview {
    width: 100%;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.5);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.point-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.point-preview-placeholder {
    color: rgba(255, 255, 255, 0.3);
    font-size: 0.7rem;
    text-align: center;
}

.point-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.point-title {
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
}

.point-author {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Environment info section */
.info-section {
    margin-bottom: 30px;
}

.info-section h3 {
    margin-bottom: 15px;
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
    letter-spacing: 1px;
    text-transform: uppercase;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

.info-section p {
    font-size: 0.85rem;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.9);
}

.info-section ul {
    list-style-type: none;
    padding-left: 5px;
}

.info-section li {
    margin-bottom: 10px;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Controls Overlay */
#controls-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none; /* Allow clicks to pass through */
}

#controls-overlay.visible {
    opacity: 1;
}

.controls-panel {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px;
    border-radius: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Bottom left movement controls */
.movement-controls {
    bottom: 20px;
    left: 20px;
    display: flex;
    align-items: center;
}

/* Top right camera controls */
.camera-controls {
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
}

/* Search hint */
.search-hint {
    top: 65px; /* Position below the search bar */
    left: 20px;
    position: absolute;
    width: 300px; /* Match search bar width */
    text-align: center;
}

.arrow-down {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(0, 0, 0, 0.7);
}

.search-text {
    font-size: 1.1rem !important;
    font-weight: 500;
    letter-spacing: 1px;
}

.control-item {
    display: flex;
    align-items: center;
    margin: 5px 10px;
}

.control-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    min-width: 30px;
}

.wasd-icon {
    font-family: 'Neue Haas Grotesk Display Pro', 'Arial', sans-serif;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 3px 5px;
    border-radius: 0;
    font-size: 0.8rem;
    letter-spacing: 1px;
}

.control-text {
    font-family: 'Neue Haas Grotesk Display Pro', 'Arial', sans-serif;
    font-size: 0.9rem;
    letter-spacing: 1px;
    font-weight: 400;
}

/* Scene Container */
#scene-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Point Tooltip */
#point-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    z-index: 1000;
    pointer-events: none;
}

/* Sidebar Toggle Button */
#toggle-sidebar {
    position: fixed;
    top: 20%; /* Positioned in the top third of the screen */
    right: 0;
    transform: none; /* Remove vertical centering */
    background-color: black;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-right: none;
    padding: 15px 5px;
    cursor: pointer;
    z-index: 99;
    writing-mode: vertical-lr;
    text-orientation: upright;
    letter-spacing: 2px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s;
    /* Rounded corners */
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

/* Hide the toggle button when sidebar is visible */
#sidebar.visible ~ #toggle-sidebar {
    opacity: 0;
    pointer-events: none;
}

#toggle-sidebar:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
