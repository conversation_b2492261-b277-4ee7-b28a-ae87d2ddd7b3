/* Common styles for all Homara pages */
@import url('https://fonts.cdnfonts.com/css/neue-haas-grotesk-display-pro');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Neue Haas Grotesk Display Pro', 'Arial', sans-serif;
}

body {
    background-color: black;
    color: white;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

header {
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 40px;
}

header h1 {
    font-size: 2rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.back-link {
    display: inline-block;
    margin-top: 20px;
    color: white;
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    transition: background-color 0.3s;
}

.back-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

section {
    margin-bottom: 40px;
}

h2 {
    font-size: 1.5rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
}

p {
    margin-bottom: 20px;
    font-size: 1rem;
    letter-spacing: 0.5px;
}

.button {
    display: inline-block;
    background-color: white;
    color: black;
    padding: 10px 20px;
    text-decoration: none;
    font-weight: 500;
    letter-spacing: 0.5px;
    margin-top: 20px;
    transition: background-color 0.3s;
}

.button:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

/* Form styles */
form {
    margin-top: 30px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

input, textarea, select {
    width: 100%;
    padding: 12px;
    background-color: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1rem;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: white;
}

button[type="submit"] {
    background-color: white;
    color: black;
    border: none;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

button[type="submit"]:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

/* Grid layout */
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.grid-item {
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
}

.grid-item h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 20px 10px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .grid {
        grid-template-columns: 1fr;
    }
}
