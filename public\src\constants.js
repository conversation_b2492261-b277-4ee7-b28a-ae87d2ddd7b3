/**
 * Constants for the Homara Website
 * Configuration values and constants used throughout the application
 */

// Firebase Configuration
window.FIREBASE_CONFIG = {
  apiKey: "AIzaSyB-WtgN67M4vQN3BZXuJzkgAewPN_ZUoLw",
  authDomain: "homara-77657.firebaseapp.com",
  projectId: "homara-77657",
  storageBucket: "homara-77657.appspot.com",
  messagingSenderId: "664988780573",
  appId: "1:664988780573:web:6d237b055d2af050319ef4",
  measurementId: "G-PW4TDPPCFS"
};

// Initialize Firebase immediately to avoid timing issues
if (typeof firebase !== 'undefined') {
    if (firebase.apps.length === 0) {
        firebase.initializeApp(window.FIREBASE_CONFIG);
        console.log('Firebase initialized from constants.js');
    }
}

// API Endpoints
window.API_BASE_URL = 'http://localhost:3002/api';

// Application Routes
window.ROUTES = {
  HOME: '/',
  BUILD: '/build',
  JOIN: '/join',
  LEARN: '/learn',
  NEWS: '/news',
  LOGIN: '/login',
  SIGNUP: '/signup',
  ABOUT: '/about',
  USER_COMMUNITY: '/:username'
};
