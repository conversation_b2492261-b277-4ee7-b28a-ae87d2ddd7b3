/**
 * URL Parser Utility
 * Extracts information from URLs for the Homara website
 */

/**
 * Extract username or builder ID from URL
 * URL format: homara.com/username or homara.com/ID
 * @returns {string|null} Username or builder ID, or null if not found
 */
export const extractUsernameFromURL = () => {
  try {
    const path = window.location.pathname;

    // Remove leading and trailing slashes
    const cleanPath = path.replace(/^\/|\/$/g, '');

    // If path is empty, return null
    if (!cleanPath) {
      return null;
    }

    // Split by slashes and get the first segment
    const segments = cleanPath.split('/');

    // If the first segment is a known route, return null
    const knownRoutes = ['build', 'join', 'learn', 'news', 'login', 'signup', 'about'];
    if (knownRoutes.includes(segments[0].toLowerCase())) {
      return null;
    }

    // Otherwise, return the first segment as the username/ID
    return segments[0];
  } catch (error) {
    console.error('Error extracting username from URL:', error);
    return null;
  }
};

/**
 * Check if the current URL is for a specific user's community
 * @returns {boolean} True if the URL is for a user's community
 */
export const isUserCommunityURL = () => {
  return !!extractUsernameFromURL();
};

/**
 * Parse URL parameters
 * @param {string} url - URL to parse (defaults to current URL)
 * @returns {Object} Object containing URL parameters
 */
export const parseURLParams = (url = window.location.href) => {
  try {
    const params = {};
    const urlObj = new URL(url);
    const searchParams = new URLSearchParams(urlObj.search);

    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }

    return params;
  } catch (error) {
    console.error('Error parsing URL parameters:', error);
    return {};
  }
};

/**
 * Get the current route from the URL
 * @returns {string} Current route
 */
export const getCurrentRoute = () => {
  try {
    const path = window.location.pathname;

    // Remove leading and trailing slashes
    const cleanPath = path.replace(/^\/|\/$/g, '');

    // If path is empty, return 'home'
    if (!cleanPath) {
      return 'home';
    }

    // Split by slashes and get the first segment
    const segments = cleanPath.split('/');
    return segments[0].toLowerCase();
  } catch (error) {
    console.error('Error getting current route:', error);
    return 'home';
  }
};

/**
 * Build a URL for a user's community
 * @param {string} username - Username
 * @returns {string} URL for the user's community
 */
export const buildUserCommunityURL = (username) => {
  if (!username) {
    return '/';
  }

  return `/${encodeURIComponent(username)}`;
};

// Export all functions as a default object
export default {
  extractUsernameFromURL,
  isUserCommunityURL,
  parseURLParams,
  getCurrentRoute,
  buildUserCommunityURL
};