<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Test - Homara</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .debug-section h3 {
            margin-top: 0;
        }
        .debug-info {
            font-family: monospace;
            font-size: 12px;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-buttons {
            margin-top: 20px;
        }
        .test-buttons button {
            margin-right: 10px;
            margin-bottom: 10px;
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Registration Test Page</h1>
        <p>This page helps test the registration flow with detailed debugging information.</p>
        
        <form id="test-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="TestPassword123!" required>
            </div>
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" value="+1234567890" required>
            </div>
            <button type="submit" id="register-button">Test Registration</button>
        </form>

        <div class="test-buttons">
            <button onclick="testFirebaseConnection()">Test Firebase Connection</button>
            <button onclick="testBackendConnection()">Test Backend Connection</button>
            <button onclick="testUsernameCheck()">Test Username Check</button>
            <button onclick="clearDebugLog()">Clear Debug Log</button>
        </div>

        <div class="debug-section">
            <h3>Debug Information</h3>
            <div id="debug-log" class="debug-info">Debug information will appear here...</div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

    <!-- Configuration -->
    <script src="config.js"></script>

    <!-- Constants -->
    <script src="src/constants.js"></script>

    <!-- MongoDB Connection -->
    <script src="src/MongoDBConnection.js"></script>

    <!-- Firebase Authentication -->
    <script src="src/FirebaseAuth.js"></script>

    <script>
        let auth;
        const debugLog = document.getElementById('debug-log');
        
        function addDebugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function clearDebugLog() {
            debugLog.textContent = '';
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('Page loaded, initializing...');
            
            // Check if Firebase config is available
            if (typeof window.FIREBASE_CONFIG !== 'undefined') {
                addDebugLog('✓ Firebase config found');
            } else {
                addDebugLog('✗ Firebase config not found');
            }
            
            // Check API base URL
            if (typeof window.API_BASE_URL !== 'undefined') {
                addDebugLog(`✓ API base URL: ${window.API_BASE_URL}`);
            } else {
                addDebugLog('✗ API base URL not found');
            }
            
            // Initialize Firebase Auth
            try {
                auth = new FirebaseAuth();
                auth.initialize();
                addDebugLog('✓ Firebase Auth initialized');
            } catch (error) {
                addDebugLog(`✗ Firebase Auth initialization failed: ${error.message}`);
            }
            
            // Set up form handler
            document.getElementById('test-form').addEventListener('submit', handleRegistration);
        });
        
        async function handleRegistration(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const username = document.getElementById('username').value;
            const phone = document.getElementById('phone').value;
            
            const button = document.getElementById('register-button');
            button.disabled = true;
            button.textContent = 'Testing...';
            
            addDebugLog('=== REGISTRATION TEST START ===');
            addDebugLog(`Email: ${email}`);
            addDebugLog(`Username: ${username}`);
            addDebugLog(`Phone: ${phone.substring(0, 3)}***`);
            
            try {
                const result = await auth.registerUser(email, password, username, phone);
                
                if (result.success) {
                    addDebugLog('✓ Registration successful!');
                    showMessage('Registration test successful!', 'success');
                } else {
                    addDebugLog(`✗ Registration failed: ${result.error?.message || 'Unknown error'}`);
                    showMessage(`Registration failed: ${result.error?.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                addDebugLog(`✗ Registration error: ${error.message}`);
                showMessage(`Registration error: ${error.message}`, 'error');
            }
            
            addDebugLog('=== REGISTRATION TEST END ===');
            
            button.disabled = false;
            button.textContent = 'Test Registration';
        }
        
        async function testFirebaseConnection() {
            addDebugLog('Testing Firebase connection...');
            try {
                if (typeof firebase !== 'undefined' && firebase.apps.length > 0) {
                    addDebugLog('✓ Firebase is connected');
                    const user = firebase.auth().currentUser;
                    addDebugLog(`Current user: ${user ? user.email : 'None'}`);
                } else {
                    addDebugLog('✗ Firebase not connected');
                }
            } catch (error) {
                addDebugLog(`✗ Firebase test error: ${error.message}`);
            }
        }
        
        async function testBackendConnection() {
            addDebugLog('Testing backend connection...');
            try {
                const response = await fetch(`${window.API_BASE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    addDebugLog(`✓ Backend connected: ${data.message || 'OK'}`);
                } else {
                    addDebugLog(`✗ Backend returned status: ${response.status}`);
                }
            } catch (error) {
                addDebugLog(`✗ Backend connection error: ${error.message}`);
            }
        }
        
        async function testUsernameCheck() {
            const username = document.getElementById('username').value;
            addDebugLog(`Testing username check for: ${username}`);
            try {
                const response = await fetch(`${window.API_BASE_URL}/auth/check-username?username=${encodeURIComponent(username)}`);
                if (response.ok) {
                    const data = await response.json();
                    addDebugLog(`✓ Username check result: ${JSON.stringify(data)}`);
                } else {
                    addDebugLog(`✗ Username check returned status: ${response.status}`);
                }
            } catch (error) {
                addDebugLog(`✗ Username check error: ${error.message}`);
            }
        }
        
        function showMessage(message, type) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.success-message, .error-message');
            existingMessages.forEach(msg => msg.remove());
            
            const messageElement = document.createElement('div');
            messageElement.className = type + '-message';
            messageElement.textContent = message;
            
            document.querySelector('.container').insertBefore(messageElement, document.getElementById('test-form'));
        }
    </script>
</body>
</html>
