<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join <PERSON></title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="icon" href="../../images/homara1.png">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content-section {
            margin-bottom: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .content-section h2 {
            color: #00ff00;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .content-section p {
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .join-form {
            background: rgba(0, 255, 0, 0.1);
            padding: 30px;
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #00ff00;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            color: #ffffff;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #00ff00;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #00ff00, #00cc00);
            color: #000000;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← Back to Homara</a>
    
    <div class="container">
        <div class="header">
            <h1>Join Homara</h1>
            <p>Become part of our digital community and help shape the future of virtual spaces</p>
        </div>
        
        <div class="content-section">
            <h2>What is Homara?</h2>
            <p>Homara is a revolutionary platform that allows communities to create and share their own 3D digital spaces. Each community is represented by a unique visual structure where members can interact, share content, and build connections.</p>
            <p>In our development phase, we're focusing on creating the core experience with a single community to perfect the user interface and interaction systems.</p>
        </div>
        
        <div class="content-section">
            <h2>Development Community</h2>
            <p>During this development phase, you'll be joining our primary test community. This community uses a tree-based visualization where each member and piece of content is represented as points of light within the structure.</p>
            <p>As a member, you'll be able to:</p>
            <ul>
                <li>Navigate through the 3D community space</li>
                <li>Search for other members</li>
                <li>Interact with community content</li>
                <li>Provide feedback on the user experience</li>
            </ul>
        </div>
        
        <div class="content-section join-form">
            <h2>Join the Development Community</h2>
            <p>Fill out the form below to request access to our development community:</p>
            
            <form id="join-form">
                <div class="form-group">
                    <label for="username">Preferred Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="interest">Why are you interested in Homara?</label>
                    <input type="text" id="interest" name="interest" placeholder="Tell us what draws you to digital communities...">
                </div>
                
                <button type="submit" class="submit-btn">Request Access</button>
            </form>
        </div>
    </div>
    
    <script>
        document.getElementById('join-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // For development, just show a success message
            alert('Thank you for your interest! During development phase, access is currently limited to internal testing. We\'ll contact you when we open up to more users.');
            
            // In production, this would submit to your backend
            console.log('Form submitted:', {
                username: document.getElementById('username').value,
                email: document.getElementById('email').value,
                interest: document.getElementById('interest').value
            });
        });
    </script>
</body>
</html>
