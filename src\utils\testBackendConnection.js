/**
 * Test Backend Connection
 * Utility to test the connection to the backend API
 */

import { testBackendConnection } from '../api/userApi';

/**
 * Test the connection to the backend API
 * This function should be called when the application initializes
 */
const testConnection = async () => {
  try {
    console.log('Testing backend connection...');
    const isConnected = await testBackendConnection();
    
    if (isConnected) {
      console.log('✅ Backend connection successful!');
    } else {
      console.error('❌ Backend connection failed!');
      // You might want to show a notification to the user here
    }
    
    return isConnected;
  } catch (error) {
    console.error('❌ Backend connection test error:', error);
    return false;
  }
};

export default testConnection;
