<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Info - Homara</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="stylesheet" href="../../sidebar.css">
    <link rel="icon" href="../../homara1.png">
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button id="toggle-sidebar">MENU</button>

    <!-- Sidebar -->
    <div id="sidebar" class="hidden">
        <div class="sidebar-header">
            <h2 id="sidebar-title">Community</h2>
            <button id="close-sidebar">×</button>
        </div>
        <div class="sidebar-content">
            <!-- Top half: Navigation links -->
            <div class="sidebar-nav">
                <div class="nav-item" data-page="join">
                    <h3>APPLY TO JOIN A COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="build">
                    <h3>BUILD YOUR OWN COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="news">
                    <h3>NEWS</h3>
                </div>
                <div class="nav-item" data-page="learn">
                    <h3>INFO</h3>
                </div>
                <div class="nav-item" data-page="home">
                    <h3>BACK TO HOMARA</h3>
                </div>
            </div>

            <!-- Divider -->
            <div class="sidebar-divider"></div>

            <!-- Bottom half: Viewed points history -->
            <div class="sidebar-history">
                <h3>RECENTLY VIEWED POINTS</h3>
                <div id="points-history" class="points-history-container">
                    <!-- Points history will be populated dynamically -->
                    <div class="empty-history-message">
                        Your viewed points will appear here
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <header>
            <h1>Info About Homara</h1>
        </header>

        <section>
            <h2>What is Homara?</h2>
            <p>
                Homara is a digital platform that allows users to create and explore 3D communities.
                Each community is represented by a unique 3D shape (like a tree) with interactive points
                that contain content shared by community members.
            </p>
            <p>
                Our mission is to create a new way for people to connect and share information in a
                visually engaging and spatially organized environment.
            </p>
        </section>

        <section>
            <h2>How Homara Works</h2>
            <div class="grid">
                <div class="grid-item">
                    <h3>Communities</h3>
                    <p>
                        Communities are the core of Homara. Each community has a unique URL (Homara.com/ID)
                        and is represented by a 3D shape with interactive points.
                    </p>
                </div>
                <div class="grid-item">
                    <h3>Points</h3>
                    <p>
                        Points are interactive elements within a community that contain content shared by members.
                        Click on a point to view its content and add it to your history.
                    </p>
                </div>
                <div class="grid-item">
                    <h3>Desktop App</h3>
                    <p>
                        The Homara desktop application allows community owners to customize their community,
                        add new points, and update content.
                    </p>
                </div>
                <div class="grid-item">
                    <h3>Navigation</h3>
                    <p>
                        Navigate through the 3D environment using your mouse:
                        Left-click + drag to rotate, right-click + drag to pan, and scroll to zoom.
                    </p>
                </div>
            </div>
        </section>

        <section>
            <h2>Technology</h2>
            <p>
                Homara is built using cutting-edge web technologies:
            </p>
            <ul>
                <li>Three.js for 3D rendering</li>
                <li>MongoDB for storing community data</li>
                <li>Google Firestore for authentication</li>
                <li>Containerized with Docker for reliable hosting</li>
                <li>Hosted on Google Cloud with AWS as a fallback</li>
            </ul>
        </section>

        <section>
            <h2>FAQ</h2>
            <div class="grid">
                <div class="grid-item">
                    <h3>How do I create my own community?</h3>
                    <p>
                        Purchase a Builder ID, download our desktop application, and start customizing your community.
                        <a href="../build/index.html">Learn more</a>
                    </p>
                </div>
                <div class="grid-item">
                    <h3>Can I join existing communities?</h3>
                    <p>
                        Yes, you can join location-based communities or apply to join private communities.
                        <a href="../join/index.html">Learn more</a>
                    </p>
                </div>
                <div class="grid-item">
                    <h3>How much does it cost?</h3>
                    <p>
                        Creating your own community requires a one-time purchase of a Builder ID.
                        Prices start at $19.99.
                    </p>
                </div>
                <div class="grid-item">
                    <h3>What can I share in my community?</h3>
                    <p>
                        You can share text, images, links, and other content through interactive points in your community.
                    </p>
                </div>
            </div>
        </section>
    </div>

    <!-- Add sidebar functionality -->
    <script src="../../src/HomaraStart/Sidebar.js"></script>
    <script>
        // Initialize sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = new Sidebar();

            // Set up event listeners for navigation items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    const page = this.getAttribute('data-page');
                    switch(page) {
                        case 'join':
                            window.location.href = '../join/index.html';
                            break;
                        case 'build':
                            window.location.href = '../build/index.html';
                            break;
                        case 'news':
                            window.location.href = '../news/index.html';
                            break;
                        case 'learn':
                            window.location.href = '../learn/index.html';
                            break;
                        case 'home':
                            window.location.href = '../../index.html';
                            break;
                    }
                });
            });
        });
    </script>
</body>
</html>
