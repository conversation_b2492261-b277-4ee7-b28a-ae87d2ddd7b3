/**
 * Constants for the Homara Website
 * Configuration values and constants used throughout the application
 */

// API Endpoints
export const API_ENDPOINTS = {
  BASE_URL: process.env.REACT_APP_API_URL || 'https://api.homara.community',
  PUBLIC: {
    COMMUNITY: '/api/public/community',
    POINTS: '/api/public/points',
    USER: '/api/public/user',
    SEARCH: '/api/public/search'
  },
  PRIVATE: {
    USER_PROFILE: '/api/user/profile',
    COMMUNITY: '/api/community',
    POINTS: '/api/community/:communityId/points'
  }
};

// Firebase Configuration
export const FIREBASE_CONFIG = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || 'YOUR_API_KEY',
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || 'YOUR_AUTH_DOMAIN',
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || 'YOUR_PROJECT_ID',
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || 'YOUR_STORAGE_BUCKET',
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || 'YOUR_MESSAGING_SENDER_ID',
  appId: process.env.REACT_APP_FIREBASE_APP_ID || 'YOUR_APP_ID'
};

// Application Routes
export const ROUTES = {
  HOME: '/',
  BUILD: '/build',
  JOIN: '/join',
  LEARN: '/learn',
  NEWS: '/news',
  LOGIN: '/login',
  SIGNUP: '/signup',
  ABOUT: '/about',
  USER_COMMUNITY: '/:username'
};

// 3D Environment Constants
export const ENVIRONMENT = {
  // Camera settings
  CAMERA: {
    INITIAL_POSITION: { x: 0, y: 70, z: 100 }, // Higher Y position
    LOOK_AT: { x: 0, y: 25, z: 0 }, // Look at the middle of the tree
    FOV: 75,
    NEAR: 0.1,
    FAR: 1000,
    CONTROLS_DURATION: 8000 // Controls overlay display duration in ms
  },

  // Default shape settings
  SHAPES: {
    TREE: {
      HEIGHT: 40,
      RADIUS_BASE: 2,
      BRANCH_LEVELS: 5,
      POINTS_PER_LEVEL: 2000,
      COLOR_VARIATION: 0.3,
      BASE_COLOR: 0x228B22,
      POINT_SIZE: 0.2
    }
  },

  // Background settings
  BACKGROUND: {
    COLOR: 0x000000 // Black background
  }
};

// UI Constants
export const UI = {
  SIDEBAR: {
    WIDTH: '300px',
    BACKGROUND_COLOR: '#ffffff',
    TEXT_COLOR: '#000000',
    FONT_FAMILY: 'Neue Haas Grotesk Display Pro, sans-serif'
  },
  MENU_BUTTON: {
    BACKGROUND_COLOR: '#ffffff',
    TEXT_COLOR: '#000000',
    BORDER_RADIUS: '10px'
  },
  SEARCH_BAR: {
    BACKGROUND_COLOR: '#ffffff',
    TEXT_COLOR: '#000000',
    PLACEHOLDER: 'Search for communities...'
  }
};

// Local Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: 'homara_user_token',
  USER_DATA: 'homara_user_data',
  ANIMATION_PLAYED: 'homara_animation_played',
  VIEWED_POINTS: 'homara_viewed_points'
};

// Export all constants as a default object
export default {
  API_ENDPOINTS,
  FIREBASE_CONFIG,
  ROUTES,
  ENVIRONMENT,
  UI,
  STORAGE_KEYS
};