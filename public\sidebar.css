/* Sidebar Toggle Button */
#toggle-sidebar {
    position: fixed;
    top: 20%; /* Positioned in the top third of the screen */
    right: 0;
    transform: none; /* Remove vertical centering */
    background-color: black;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-right: none;
    padding: 15px 5px;
    cursor: pointer;
    z-index: 99;
    writing-mode: vertical-lr;
    text-orientation: upright;
    letter-spacing: 2px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s;
    /* Rounded corners */
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

#toggle-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Hide toggle button when sidebar is open */
body.sidebar-open #toggle-sidebar {
    display: none;
}

/* Sidebar */
#sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: black;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 100;
    transition: transform 0.3s ease;
    overflow-y: auto;
    transform: translateX(100%);
}

#sidebar.visible {
    transform: translateX(0);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

#sidebar-title {
    font-size: 1.2rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin: 0;
}

#close-sidebar {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.sidebar-content {
    padding: 20px;
}

.sidebar-nav {
    margin-bottom: 30px;
}

.nav-item {
    margin-bottom: 15px;
    cursor: pointer;
    transition: opacity 0.3s;
}

.nav-item:hover {
    opacity: 0.7;
}

.nav-item h3 {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin: 0;
}

.sidebar-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 20px 0;
}

.sidebar-history h3 {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    margin-bottom: 15px;
}

.points-history-container {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 10px;
}

.history-item-content {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.history-item-meta {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.empty-history-message {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}
