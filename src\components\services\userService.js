/**
 * User Service
 * Handles all operations related to user data
 */
import api from './api';

// Service for handling user data
const userService = {
  /**
   * Get user data by username
   * @param {string} username - Username
   * @returns {Promise<Object>} User data
   */
  getUserByUsername: async (username) => {
    try {
      return await api.getUserByUsername(username);
    } catch (error) {
      console.error(`Failed to get user with username ${username}:`, error);
      throw error;
    }
  },

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile
   */
  getCurrentUserProfile: async () => {
    try {
      return await api.getUserProfile();
    } catch (error) {
      console.error('Failed to get current user profile:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Updated user profile
   */
  updateUserProfile: async (profileData) => {
    try {
      return await api.updateUserProfile(profileData);
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw error;
    }
  },

  /**
   * Get user's community
   * @param {string} username - Username
   * @returns {Promise<Object>} Community data
   */
  getUserCommunity: async (username) => {
    try {
      const user = await api.getUserByUsername(username);

      if (!user || !user.communityId) {
        throw new Error(`User ${username} has no associated community`);
      }

      return await api.getCommunity(user.communityId);
    } catch (error) {
      console.error(`Failed to get community for user ${username}:`, error);
      throw error;
    }
  },

  /**
   * Check if a username exists
   * @param {string} username - Username to check
   * @returns {Promise<boolean>} True if username exists
   */
  checkUsernameExists: async (username) => {
    try {
      const user = await api.getUserByUsername(username);
      return !!user; // Convert to boolean
    } catch (error) {
      // If we get a 404, the username doesn't exist
      if (error.message.includes('404')) {
        return false;
      }

      // For other errors, rethrow
      console.error(`Error checking if username ${username} exists:`, error);
      throw error;
    }
  },

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  registerUser: async (userData) => {
    try {
      // This would typically call a Firebase Auth method
      // For now, we'll simulate it
      console.log('Registering user:', userData);

      // Check if username already exists
      const usernameExists = await userService.checkUsernameExists(userData.username);
      if (usernameExists) {
        throw new Error(`Username ${userData.username} is already taken`);
      }

      // In a real implementation, this would call Firebase Auth
      // and then store additional user data in the database

      // For now, return a simulated success response
      return {
        success: true,
        user: {
          uid: 'simulated-uid-' + Date.now(),
          username: userData.username,
          email: userData.email,
          phoneNumber: userData.phoneNumber,
          createdAt: new Date()
        }
      };
    } catch (error) {
      console.error('Failed to register user:', error);
      throw error;
    }
  },

  /**
   * Get user's builder ID
   * @param {string} username - Username
   * @returns {Promise<string>} Builder ID
   */
  getUserBuilderId: async (username) => {
    try {
      const user = await api.getUserByUsername(username);

      if (!user) {
        throw new Error(`User ${username} not found`);
      }

      // In this implementation, builder ID is the same as username
      return user.builderId || username;
    } catch (error) {
      console.error(`Failed to get builder ID for user ${username}:`, error);
      throw error;
    }
  }
};

export default userService;