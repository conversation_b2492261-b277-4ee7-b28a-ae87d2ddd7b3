<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Homara</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="images/homara1.png" type="image/png">

    <!-- Three.js scripts - using a stable version -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>

    <!-- Add a simple test script to verify Three.js is loaded -->
    <script>
        console.log('Three.js loaded in head:', typeof THREE !== 'undefined');
        if (typeof THREE === 'undefined') {
            console.error('THREE is not defined! Trying alternative CDN...');
            document.write('<script src="https://unpkg.com/three@0.128.0/build/three.min.js"><\/script>');
            document.write('<script src="https://unpkg.com/three@0.128.0/examples/js/loaders/OBJLoader.js"><\/script>');
            document.write('<script src="https://unpkg.com/three@0.128.0/examples/js/controls/OrbitControls.js"><\/script>');
        }
    </script>
</head>
<body>
    <!-- The animation will be created dynamically by StartAnimate.js -->

    <div id="search-container" class="hidden">
        <div class="search-wrapper">
            <input type="text" id="search-input" placeholder="Search Username or Location">
            <div id="search-dropdown" class="hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>
    </div>

    <div id="sidebar" class="hidden">
        <div class="sidebar-header">
            <h2 id="sidebar-title">Community</h2>
            <button id="close-sidebar">×</button>
        </div>
        <div class="sidebar-content">
            <!-- Top half: Navigation links -->
            <div class="sidebar-nav">
                <div class="nav-item" data-page="join">
                    <h3>APPLY TO JOIN A COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="build">
                    <h3>BUILD YOUR OWN COMMUNITY</h3>
                </div>
                <div class="nav-item" data-page="news">
                    <h3>NEWS</h3>
                </div>
                <div class="nav-item" data-page="learn">
                    <h3>INFO</h3>
                </div>
                <div class="nav-item" data-page="home">
                    <h3>BACK TO HOMARA</h3>
                </div>
            </div>

            <!-- Divider -->
            <div class="sidebar-divider"></div>

            <!-- Bottom half: Viewed points history -->
            <div class="sidebar-history">
                <h3>RECENTLY VIEWED POINTS</h3>
                <div id="points-history" class="points-history-container">
                    <!-- Points history will be populated dynamically -->
                    <div class="empty-history-message">
                        Your viewed points will appear here
                    </div>
                </div>
            </div>

            <!-- Environment info section removed -->
        </div>
    </div>

    <div id="controls-overlay" class="hidden">
        <!-- Bottom left movement controls -->
        <div class="controls-panel movement-controls">
            <div class="control-item">
                <div class="control-icon wasd-icon">W A S D</div>
                <div class="control-text">Movement</div>
            </div>
            <div class="control-item">
                <div class="control-icon scroll-icon">
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="white" d="M12,0C7.58,0,4,3.58,4,8v8c0,4.42,3.58,8,8,8s8-3.58,8-8V8C20,3.58,16.42,0,12,0z M12,2c3.31,0,6,2.69,6,6v8 c0,3.31-2.69,6-6,6s-6-2.69-6-6V8C6,4.69,8.69,2,12,2z M12,6c-0.55,0-1,0.45-1,1v3c0,0.55,0.45,1,1,1s1-0.45,1-1V7 C13,6.45,12.55,6,12,6z"/>
                    </svg>
                </div>
                <div class="control-text">Zoom</div>
            </div>
        </div>

        <!-- No top right camera controls or search hint -->
    </div>

    <!-- Our JavaScript files -->
    <script>
        // Verify Three.js is loaded before loading our scripts
        console.log('Before loading our scripts, THREE available:', typeof THREE !== 'undefined');

        // If THREE is not available, try to load it again
        if (typeof THREE === 'undefined') {
            console.error('THREE still not defined! Loading directly from unpkg...');
            document.write('<script src="https://unpkg.com/three@0.128.0/build/three.min.js"><\/script>');
        }
    </script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

    <!-- Configuration -->
    <script src="config.js"></script>

    <!-- Constants -->
    <script src="src/constants.js"></script>

    <!-- MongoDB Connection -->
    <script src="src/MongoDBConnection.js"></script>

    <!-- Firebase Authentication -->
    <script src="src/FirebaseAuth.js"></script>

    <!-- HomaraStart scripts -->
    <script src="src/HomaraStart/CameraController.js"></script>
    <script src="src/HomaraStart/StartAnimate.js"></script>
    <script src="src/HomaraStart/Environment.js"></script>
    <script src="src/HomaraStart/controls-overlay.js"></script>
    <script src="src/HomaraStart/Sidebar.js"></script>
    <script src="src/HomaraStart/Search.js"></script>
    <!-- New Point Menu System -->
    <script src="src/HomaraStart/PointMenuAnimation.js"></script>
    <script src="src/HomaraStart/CleanMenu.js"></script>
    <script src="src/HomaraStart/PointMenu.js"></script>
    <!-- Tree shape script -->
    <script src="src/HomaraStart/shapes/tree.js"></script>
    <script src="src/HomaraStart/EnvLoader.js"></script>

    <!-- Main script - load this directly -->
    <script src="src/HomaraStart/main.js"></script>
</body>
</html>