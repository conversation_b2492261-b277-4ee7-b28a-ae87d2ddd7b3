<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Why Homara - Development</title>
    <link rel="stylesheet" href="../../common.css">
    <link rel="icon" href="../../images/homara1.png">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #00ff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content-section {
            margin-bottom: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .content-section h2 {
            color: #00ff00;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .content-section p {
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .vision-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .vision-card {
            background: rgba(0, 255, 0, 0.1);
            padding: 25px;
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            transition: transform 0.3s ease;
        }
        
        .vision-card:hover {
            transform: translateY(-5px);
        }
        
        .vision-card h3 {
            color: #00ff00;
            margin-bottom: 15px;
        }
        
        .highlight {
            background: linear-gradient(45deg, #00ff00, #00cc00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <a href="../../index.html" class="back-button">← Back to Homara</a>
    
    <div class="container">
        <div class="header">
            <h1>Why Homara?</h1>
            <p>Understanding our vision for the future of digital communities</p>
        </div>
        
        <div class="content-section">
            <h2>The Problem with Current Digital Spaces</h2>
            <p>Traditional social media and online communities are flat, disconnected, and often overwhelming. They lack the spatial awareness and organic discovery that makes real-world communities thrive.</p>
            <p>Most digital platforms focus on content consumption rather than <span class="highlight">community building</span> and <span class="highlight">meaningful connections</span>.</p>
        </div>
        
        <div class="content-section">
            <h2>Our Vision</h2>
            <p>Homara reimagines digital communities as <span class="highlight">living, breathing 3D spaces</span> where every member and piece of content has a place in a beautiful, navigable environment.</p>
            
            <div class="vision-grid">
                <div class="vision-card">
                    <h3>Spatial Communities</h3>
                    <p>Communities exist as 3D structures that you can explore, navigate, and understand at a glance. Each community has its own unique shape and personality.</p>
                </div>
                
                <div class="vision-card">
                    <h3>Organic Discovery</h3>
                    <p>Find content and connections naturally by exploring the space, rather than being fed by algorithms. Discovery becomes an adventure.</p>
                </div>
                
                <div class="vision-card">
                    <h3>Visual Identity</h3>
                    <p>Every community has a distinct visual representation that reflects its culture, values, and member interactions.</p>
                </div>
                
                <div class="vision-card">
                    <h3>Meaningful Scale</h3>
                    <p>Communities are sized appropriately for human connection - not endless feeds, but spaces you can actually know and navigate.</p>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <h2>Why 3D Visualization?</h2>
            <p>Humans are spatial beings. We understand relationships, hierarchies, and communities better when we can see them in space. A 3D community structure provides:</p>
            <ul>
                <li><strong>Intuitive Navigation:</strong> Move through the community like exploring a real place</li>
                <li><strong>Visual Relationships:</strong> See how members and content relate to each other</li>
                <li><strong>Memorable Experiences:</strong> Spatial memory makes communities more memorable and engaging</li>
                <li><strong>Natural Boundaries:</strong> Communities have clear edges and centers, creating belonging</li>
            </ul>
        </div>
        
        <div class="content-section">
            <h2>Development Philosophy</h2>
            <p>We're building Homara with a focus on <span class="highlight">quality over quantity</span>. Rather than trying to connect everyone to everything, we're creating tools for meaningful, focused communities.</p>
            <p>Our development approach prioritizes:</p>
            <ul>
                <li>User experience and interface design</li>
                <li>Performance and accessibility</li>
                <li>Community feedback and iteration</li>
                <li>Sustainable, human-scale growth</li>
            </ul>
        </div>
        
        <div class="content-section">
            <h2>The Future</h2>
            <p>We envision a web where communities are beautiful, explorable spaces that bring people together around shared interests and values. Where digital interaction feels more like visiting a place than consuming content.</p>
            <p>Homara is our contribution to building that future, one community at a time.</p>
        </div>
    </div>
</body>
</html>
