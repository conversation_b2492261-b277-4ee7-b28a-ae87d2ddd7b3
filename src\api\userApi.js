/**
 * User API
 * Proxy API for user-related operations
 */

// Import Firebase auth functions
import { getAuth } from 'firebase/auth';
import { app } from '../firebase/config';

// Base API URL from environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

/**
 * Get the current Firebase user
 * @returns {Object|null} Firebase user object or null if not logged in
 */
const getCurrentUser = () => {
  const auth = getAuth(app);
  return auth.currentUser;
};

/**
 * Get Firebase ID token for the current user
 * @returns {Promise<string|null>} ID token or null if not logged in
 */
const getIdToken = async () => {
  const user = getCurrentUser();
  if (!user) return null;

  try {
    return await user.getIdToken(true);
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
};

/**
 * Make an authenticated API request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response
 */
const makeAuthenticatedRequest = async (endpoint, options = {}) => {
  // Get the current user's ID token from Firebase
  const user = getCurrentUser();
  let idToken = '';

  if (user) {
    idToken = await getIdToken();
  } else {
    throw new Error('User not authenticated');
  }

  // Set up headers with the token
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${idToken}`,
    ...options.headers
  };

  // Make the request
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `API error: ${response.status}`);
  }

  return await response.json();
};

/**
 * Safe API call wrapper with error handling
 * @param {Function} apiFunction - API function to call
 * @param {...any} args - Arguments to pass to the API function
 * @returns {Promise<Object>} API response or error
 */
const safeApiCall = async (apiFunction, ...args) => {
  try {
    return await apiFunction(...args);
  } catch (error) {
    console.error('API call failed:', error);
    // Handle error appropriately (show notification, redirect, etc.)
    throw error;
  }
};

/**
 * Check if a username is available
 * @param {string} username - Username to check
 * @returns {Promise<Object>} Result with available property
 */
export const checkUsernameAvailability = async (username) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/check-username?username=${encodeURIComponent(username)}`);

    if (!response.ok) {
      throw new Error('Failed to check username availability');
    }

    return await response.json();
  } catch (error) {
    console.error('Error checking username availability:', error);
    throw error;
  }
};

/**
 * Create a new user
 * @param {Object} userData - User data
 * @returns {Promise<Object>} Created user
 */
export const createUser = async (userData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to create user');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Send verification email
 * @param {string} email - Email address
 * @returns {Promise<Object>} Result
 */
export const sendVerificationEmail = async (email) => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/send-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Failed to send verification email');
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending verification email:', error);
    throw error;
  }
};

/**
 * Get user profile (authenticated)
 * @returns {Promise<Object>} User profile
 */
export const getUserProfile = async () => {
  return await makeAuthenticatedRequest('/user/profile', { method: 'GET' });
};

/**
 * Update user profile (authenticated)
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} Updated user profile
 */
export const updateUserProfile = async (profileData) => {
  return await makeAuthenticatedRequest('/user/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData)
  });
};

/**
 * Test backend connection
 * @returns {Promise<boolean>} True if connection successful
 */
export const testBackendConnection = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.json();
    console.log('Backend connection successful:', data);
    return true;
  } catch (error) {
    console.error('Backend connection failed:', error);
    return false;
  }
};

// Export all functions as a default object
export default {
  checkUsernameAvailability,
  createUser,
  sendVerificationEmail,
  getUserProfile,
  updateUserProfile,
  makeAuthenticatedRequest,
  safeApiCall,
  testBackendConnection
};
