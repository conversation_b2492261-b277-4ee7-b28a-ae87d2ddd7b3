/**
 * State Management for 3D Environment
 * Simple state management solution for the Homara 3D environment
 */

import { STORAGE_KEYS } from './utils/constants';

// State object
const state = {
  // User state
  user: {
    isLoggedIn: false,
    username: null,
    userId: null,
    token: null,
    profile: null
  },
  
  // Community state
  community: {
    currentCommunity: null,
    communityId: null,
    builderId: null,
    communityShape: null,
    points: []
  },
  
  // UI state
  ui: {
    sidebarOpen: true,
    searchQuery: '',
    searchResults: [],
    selectedPoint: null,
    viewedPoints: [],
    animationPlayed: false,
    isLoading: false,
    error: null
  },
  
  // Listeners for state changes
  listeners: {
    user: [],
    community: [],
    ui: []
  }
};

/**
 * Initialize state from local storage
 */
export const initializeState = () => {
  try {
    // Check if user is logged in
    const userToken = localStorage.getItem(STORAGE_KEYS.USER_TOKEN);
    const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    const animationPlayed = localStorage.getItem(STORAGE_KEYS.ANIMATION_PLAYED);
    const viewedPoints = localStorage.getItem(STORAGE_KEYS.VIEWED_POINTS);
    
    // Update state with stored values
    if (userToken) {
      state.user.token = userToken;
      state.user.isLoggedIn = true;
    }
    
    if (userData) {
      const parsedUserData = JSON.parse(userData);
      state.user.username = parsedUserData.username;
      state.user.userId = parsedUserData.userId;
      state.user.profile = parsedUserData;
    }
    
    if (animationPlayed) {
      state.ui.animationPlayed = animationPlayed === 'true';
    }
    
    if (viewedPoints) {
      state.ui.viewedPoints = JSON.parse(viewedPoints);
    }
    
    console.log('State initialized from local storage');
  } catch (error) {
    console.error('Error initializing state from local storage:', error);
  }
};

/**
 * Update user state
 * @param {Object} userData - User data to update
 */
export const updateUserState = (userData) => {
  state.user = { ...state.user, ...userData };
  
  // Save to local storage
  if (userData.token) {
    localStorage.setItem(STORAGE_KEYS.USER_TOKEN, userData.token);
  }
  
  if (userData.username || userData.userId || userData.profile) {
    localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify({
      username: state.user.username,
      userId: state.user.userId,
      ...state.user.profile
    }));
  }
  
  // Notify listeners
  notifyListeners('user');
};

/**
 * Update community state
 * @param {Object} communityData - Community data to update
 */
export const updateCommunityState = (communityData) => {
  state.community = { ...state.community, ...communityData };
  
  // Notify listeners
  notifyListeners('community');
};

/**
 * Update UI state
 * @param {Object} uiData - UI data to update
 */
export const updateUIState = (uiData) => {
  state.ui = { ...state.ui, ...uiData };
  
  // Save certain UI state to local storage
  if (uiData.animationPlayed !== undefined) {
    localStorage.setItem(STORAGE_KEYS.ANIMATION_PLAYED, uiData.animationPlayed);
  }
  
  if (uiData.viewedPoints) {
    localStorage.setItem(STORAGE_KEYS.VIEWED_POINTS, JSON.stringify(uiData.viewedPoints));
  }
  
  // Notify listeners
  notifyListeners('ui');
};

/**
 * Add a point to viewed points
 * @param {Object} point - Point that was viewed
 */
export const addViewedPoint = (point) => {
  // Check if point is already in viewed points
  const pointExists = state.ui.viewedPoints.some(p => p.id === point.id);
  
  if (!pointExists) {
    const updatedViewedPoints = [...state.ui.viewedPoints, point];
    
    // Keep only the last 10 viewed points
    if (updatedViewedPoints.length > 10) {
      updatedViewedPoints.shift();
    }
    
    updateUIState({ viewedPoints: updatedViewedPoints });
  }
};

/**
 * Clear user state (logout)
 */
export const clearUserState = () => {
  state.user = {
    isLoggedIn: false,
    username: null,
    userId: null,
    token: null,
    profile: null
  };
  
  // Remove from local storage
  localStorage.removeItem(STORAGE_KEYS.USER_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER_DATA);
  
  // Notify listeners
  notifyListeners('user');
};

/**
 * Subscribe to state changes
 * @param {string} stateKey - State key to subscribe to ('user', 'community', or 'ui')
 * @param {Function} listener - Listener function
 * @returns {Function} Unsubscribe function
 */
export const subscribe = (stateKey, listener) => {
  if (!state.listeners[stateKey]) {
    console.error(`Invalid state key: ${stateKey}`);
    return () => {};
  }
  
  state.listeners[stateKey].push(listener);
  
  // Return unsubscribe function
  return () => {
    state.listeners[stateKey] = state.listeners[stateKey].filter(l => l !== listener);
  };
};

/**
 * Notify listeners of state changes
 * @param {string} stateKey - State key that changed
 */
const notifyListeners = (stateKey) => {
  if (!state.listeners[stateKey]) {
    return;
  }
  
  state.listeners[stateKey].forEach(listener => {
    listener(state[stateKey]);
  });
};

/**
 * Get current state
 * @param {string} stateKey - State key to get ('user', 'community', or 'ui')
 * @returns {Object} Current state
 */
export const getState = (stateKey) => {
  return state[stateKey] ? { ...state[stateKey] } : null;
};

// Export all functions
export default {
  initializeState,
  updateUserState,
  updateCommunityState,
  updateUIState,
  addViewedPoint,
  clearUserState,
  subscribe,
  getState
};
