class Search {
    constructor(onSearch) {
        this.searchContainer = document.getElementById('search-container');
        this.searchInput = document.getElementById('search-input');
        this.searchDropdown = document.getElementById('search-dropdown');
        this.onSearch = onSearch || this.defaultSearchHandler;

        // Mock database of usernames and locations
        this.searchDatabase = [
            { id: 'homara1', type: 'username', url: '/homara1' },
            { id: 'community2', type: 'username', url: '/community2' },
            { id: 'community3', type: 'username', url: '/community3' },
            { id: 'NewYork', type: 'location', url: '/NewYork' },
            { id: 'LosAngeles', type: 'location', url: '/LosAngeles' },
            { id: 'Chicago', type: 'location', url: '/Chicago' },
            { id: 'SanFrancisco', type: 'location', url: '/SanFrancisco' },
            { id: 'Boston', type: 'location', url: '/Boston' },
            { id: 'Seattle', type: 'location', url: '/Seattle' },
            { id: 'Austin', type: 'location', url: '/Austin' }
        ];

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Input event for real-time filtering
        this.searchInput.addEventListener('input', () => {
            this.filterResults();
        });

        // Focus event to show dropdown
        this.searchInput.addEventListener('focus', () => {
            this.showDropdown();
        });

        // Click outside to hide dropdown
        document.addEventListener('click', (event) => {
            if (!this.searchContainer.contains(event.target)) {
                this.hideDropdown();
            }
        });

        // Enter key press in search input
        this.searchInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                // If dropdown is visible and has results, navigate to the first result
                const firstResult = this.searchDropdown.querySelector('.search-result');
                if (firstResult) {
                    this.navigateToResult(firstResult.dataset.url);
                }
            }
        });
    }

    filterResults() {
        const query = this.searchInput.value.trim().toLowerCase();

        if (query.length === 0) {
            // Show all results if query is empty
            this.populateDropdown(this.searchDatabase);
        } else {
            // Filter results based on query
            const filteredResults = this.searchDatabase.filter(item =>
                item.id.toLowerCase().includes(query) ||
                item.type.toLowerCase().includes(query)
            );

            this.populateDropdown(filteredResults);
        }

        // Show dropdown if we have results
        if (this.searchDropdown.children.length > 0) {
            this.showDropdown();
        } else {
            this.hideDropdown();
        }
    }

    populateDropdown(results) {
        // Clear previous results
        this.searchDropdown.innerHTML = '';

        // Add new results
        results.forEach(result => {
            const resultElement = document.createElement('div');
            resultElement.className = 'search-result';
            resultElement.dataset.url = result.url;

            const nameElement = document.createElement('div');
            nameElement.className = 'search-result-name';
            nameElement.textContent = result.id;

            const typeElement = document.createElement('div');
            typeElement.className = 'search-result-type';
            typeElement.textContent = result.type;

            resultElement.appendChild(nameElement);
            resultElement.appendChild(typeElement);

            // Add click event to navigate
            resultElement.addEventListener('click', () => {
                this.navigateToResult(result.url);
            });

            this.searchDropdown.appendChild(resultElement);
        });
    }

    navigateToResult(url) {
        // Open the URL in a new tab
        window.open(`https://homara.com${url}`, '_blank');

        // Clear the search input and hide dropdown
        this.searchInput.value = '';
        this.hideDropdown();
    }

    showDropdown() {
        this.searchDropdown.classList.remove('hidden');
    }

    hideDropdown() {
        this.searchDropdown.classList.add('hidden');
    }

    defaultSearchHandler(query) {
        console.log('Search query:', query);
        // Default implementation - just log the query
        // This would be replaced with actual search functionality
    }

    show() {
        this.searchContainer.classList.remove('hidden');
        // Fade in effect
        setTimeout(() => {
            this.searchContainer.style.opacity = '1';

            // Populate dropdown with all results initially
            this.populateDropdown(this.searchDatabase);
        }, 10);
    }

    hide() {
        // Fade out effect
        this.searchContainer.style.opacity = '0';
        setTimeout(() => {
            this.searchContainer.classList.add('hidden');
        }, 500);
    }

    toggle() {
        if (this.searchContainer.classList.contains('hidden')) {
            this.show();
        } else {
            this.hide();
        }
    }

    setSearchHandler(handler) {
        if (typeof handler === 'function') {
            this.onSearch = handler;
        }
    }
}

// Export the Search class
window.Search = Search;
