// Configuration variables for the application
// This file is loaded before the application and sets global configuration

// API Base URL - Use the frontend server as a proxy to avoid CORS issues
window.API_BASE_URL = 'http://localhost:3001/api';

// Frontend URL
window.FRONTEND_URL = 'http://localhost:3001';

// Environment
window.ENV = 'development';

console.log('Configuration loaded:', {
  API_BASE_URL: window.API_BASE_URL,
  FRONTEND_URL: window.FRONTEND_URL,
  ENV: window.ENV
});
