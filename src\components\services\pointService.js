/**
 * Point Service
 * Handles all operations related to point data
 */
import api from './api';

// Service for handling point data
const pointService = {
  /**
   * Get all points for a community
   * @param {string} communityId - Community ID
   * @returns {Promise<Array>} Array of points
   */
  getPointsByCommunityId: async (communityId) => {
    try {
      return await api.getCommunityPoints(communityId);
    } catch (error) {
      console.error(`Failed to get points for community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Get a specific point by ID
   * @param {string} communityId - Community ID
   * @param {string} pointId - Point ID
   * @returns {Promise<Object>} Point data
   */
  getPointById: async (communityId, pointId) => {
    try {
      const points = await api.getCommunityPoints(communityId);
      const point = points.find(p => p.id === pointId);

      if (!point) {
        throw new Error(`Point ${pointId} not found in community ${communityId}`);
      }

      return point;
    } catch (error) {
      console.error(`Failed to get point ${pointId} in community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Add a new point to a community
   * @param {string} communityId - Community ID
   * @param {Object} pointData - Point data (x, y, z, content)
   * @returns {Promise<Object>} Created point
   */
  addPoint: async (communityId, pointData) => {
    try {
      // Validate point data
      if (!pointData.x || !pointData.y || !pointData.z) {
        throw new Error('Point data must include x, y, and z coordinates');
      }

      return await api.addPoint(communityId, pointData);
    } catch (error) {
      console.error(`Failed to add point to community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Update an existing point
   * @param {string} communityId - Community ID
   * @param {string} pointId - Point ID
   * @param {Object} pointData - Updated point data
   * @returns {Promise<Object>} Updated point
   */
  updatePoint: async (communityId, pointId, pointData) => {
    try {
      return await api.updatePoint(communityId, pointId, pointData);
    } catch (error) {
      console.error(`Failed to update point ${pointId} in community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a point
   * @param {string} communityId - Community ID
   * @param {string} pointId - Point ID
   * @returns {Promise<Object>} Result of deletion
   */
  deletePoint: async (communityId, pointId) => {
    try {
      return await api.deletePoint(communityId, pointId);
    } catch (error) {
      console.error(`Failed to delete point ${pointId} from community ${communityId}:`, error);
      throw error;
    }
  },

  /**
   * Format point data for Three.js
   * @param {Array} points - Array of point data
   * @returns {Array} Formatted points for Three.js
   */
  formatPointsForThreeJS: (points) => {
    return points.map(point => ({
      position: new THREE.Vector3(point.x, point.y, point.z),
      content: point.content,
      id: point.id,
      userData: point.userData || {}
    }));
  },

  /**
   * Convert Three.js points to API format
   * @param {Array} threePoints - Array of Three.js points
   * @returns {Array} Points in API format
   */
  convertThreePointsToApiFormat: (threePoints) => {
    return threePoints.map(point => ({
      x: point.position.x,
      y: point.position.y,
      z: point.position.z,
      content: point.content,
      id: point.id,
      userData: point.userData
    }));
  }
};

export default pointService;