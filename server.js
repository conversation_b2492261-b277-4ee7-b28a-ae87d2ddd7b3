const express = require('express');
const path = require('path');
const cors = require('cors');
const app = express();
const port = process.env.PORT || 3001; // This is the frontend server port

// Enable CORS for all routes with specific configuration
app.use(cors({
  origin: ['http://localhost:3001', 'http://localhost:3002'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Parse JSON request body
app.use(express.json());

// Serve static files from the public directory
app.use(express.static('public'));

// Serve src files with correct MIME type
app.use('/src', express.static(path.join(__dirname, 'src'), {
  setHeaders: (res, filePath) => {
    if (path.extname(filePath) === '.js') {
      res.setHeader('Content-Type', 'application/javascript');
    }
  }
}));

// Serve the main index.html file for the root route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Handle Menu routes
app.get('/Menu/:section/index.html', (req, res) => {
  const section = req.params.section;
  res.sendFile(path.join(__dirname, 'public', 'Menu', section, 'index.html'));
});

// Add a health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running', timestamp: new Date().toISOString() });
});

// API proxy to forward requests to the backend
const axios = require('axios');
const BACKEND_URL = 'http://localhost:3002';

// Proxy middleware for API requests
app.use('/api', async (req, res) => {
  try {
    console.log(`Proxying ${req.method} request to ${BACKEND_URL}${req.originalUrl}`);
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    // Forward the request to the backend
    const response = await axios({
      method: req.method,
      url: `${BACKEND_URL}${req.originalUrl}`,
      data: req.body,
      headers: {
        'Content-Type': 'application/json',
        // Forward authorization header if present
        ...(req.headers.authorization && { 'Authorization': req.headers.authorization })
      }
    });

    // Log the response
    console.log(`Backend responded with status: ${response.status}`);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    // Send the backend response back to the client
    res.status(response.status).json(response.data);
  } catch (error) {
    console.error('API proxy error:', error.message);

    // Log more details about the error
    if (error.response) {
      console.error('Backend error response:', {
        status: error.response.status,
        data: error.response.data
      });
      res.status(error.response.status).json(error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from backend');
      res.status(503).json({
        error: 'Service Unavailable',
        message: 'No response received from the backend server'
      });
    } else {
      // Something happened in setting up the request
      console.error('Error setting up request:', error.message);
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message
      });
    }
  }
});

// Fallback route for SPA - handle any routes that aren't matched
app.get('*', (req, res) => {
  // Check if the request is for a file with an extension
  if (path.extname(req.path) !== '') {
    // If it's a file request that wasn't found, return 404
    return res.status(404).send('File not found');
  }

  // For all other routes, serve the main index.html
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
