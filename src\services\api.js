/**
 * API Connection Module
 * Handles all HTTP requests to the backend API
 */
import { getIdToken, getCurrentUser } from '../firebase/auth';

// Base API service for all HTTP requests to your backend
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://api.homara.community';

/**
 * Get authentication headers for API requests
 * @returns {Promise<Object>} Headers object with authentication token
 */
const getAuthHeaders = async () => {
  try {
    // Get the current user
    const user = getCurrentUser();
    
    // If no user is logged in, return empty headers
    if (!user) {
      return {};
    }
    
    // Get the Firebase ID token
    const token = await getIdToken();
    
    // If no token, return empty headers
    if (!token) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${token}`
    };
  } catch (error) {
    console.error('Error getting auth headers:', error);
    return {};
  }
};

/**
 * Generic GET request
 * @param {string} endpoint - API endpoint
 * @param {boolean} requiresAuth - Whether the request requires authentication
 * @returns {Promise<Object>} API response
 */
const fetchData = async (endpoint, requiresAuth = false) => {
  try {
    const headers = requiresAuth ? await getAuthHeaders() : {};

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * Generic POST request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body
 * @param {boolean} requiresAuth - Whether the request requires authentication
 * @returns {Promise<Object>} API response
 */
const postData = async (endpoint, data, requiresAuth = true) => {
  try {
    const headers = requiresAuth ? await getAuthHeaders() : {};

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API POST request failed:', error);
    throw error;
  }
};

/**
 * Generic PUT request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body
 * @param {boolean} requiresAuth - Whether the request requires authentication
 * @returns {Promise<Object>} API response
 */
const putData = async (endpoint, data, requiresAuth = true) => {
  try {
    const headers = requiresAuth ? await getAuthHeaders() : {};

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API PUT request failed:', error);
    throw error;
  }
};

/**
 * Generic DELETE request
 * @param {string} endpoint - API endpoint
 * @param {boolean} requiresAuth - Whether the request requires authentication
 * @returns {Promise<Object>} API response
 */
const deleteData = async (endpoint, requiresAuth = true) => {
  try {
    const headers = requiresAuth ? await getAuthHeaders() : {};

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API DELETE request failed:', error);
    throw error;
  }
};

// Export different API methods
export const api = {
  // Public API endpoints (no authentication required)

  // Get public community data
  getCommunity: (communityId) =>
    fetchData(`/api/public/community/${communityId}`, false),

  // Get points for a community
  getCommunityPoints: (communityId) =>
    fetchData(`/api/public/points/${communityId}`, false),

  // Find user and their associated community
  getUserByUsername: (username) =>
    fetchData(`/api/public/user/${username}`, false),

  // Search for communities
  searchCommunities: (query) =>
    fetchData(`/api/public/search?q=${encodeURIComponent(query)}`, false),

  // Private API endpoints (authentication required)

  // Get user profile
  getUserProfile: () =>
    fetchData('/api/user/profile', true),

  // Update user profile
  updateUserProfile: (profileData) =>
    putData('/api/user/profile', profileData, true),

  // Create a new community
  createCommunity: (communityData) =>
    postData('/api/community', communityData, true),

  // Update community data
  updateCommunity: (communityId, communityData) =>
    putData(`/api/community/${communityId}`, communityData, true),

  // Add a point to a community
  addPoint: (communityId, pointData) =>
    postData(`/api/community/${communityId}/points`, pointData, true),

  // Update a point
  updatePoint: (communityId, pointId, pointData) =>
    putData(`/api/community/${communityId}/points/${pointId}`, pointData, true),

  // Delete a point
  deletePoint: (communityId, pointId) =>
    deleteData(`/api/community/${communityId}/points/${pointId}`, true)
};

export default api;
